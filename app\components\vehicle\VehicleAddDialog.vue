<script lang="ts" setup>
import z from "zod";
import type { FormSubmitEvent } from "@nuxt/ui";
import { useOptionsStore } from "~/stores/options";
import type { ApiResponse } from "~/types";

const { $api } = useNuxtApp();
const { t } = useI18n();
const toast = useToast();
const optionsStore = useOptionsStore();
const {
  optionsColor,
  optionsEngine,
  optionsProvince,
  optionsVehicleModel,
} = storeToRefs(optionsStore);

const vehicleSchema = z.object({
  id: z.string().optional(),
  organizationId: z.string(),
  description: z.string(),
  licensePlate: z.string(),
  provinceId: z.string(),
  modelId: z.string(),
  color: z.string(),
});
type Schema = z.output<typeof vehicleSchema>;

interface Props {
  vehicle?: Partial<Schema>;
  isDialogVisible: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  vehicle: () => ({
    id: undefined,
    organizationId: undefined,
    description: undefined,
    licensePlate: undefined,
    provinceId: undefined,
    modelId: undefined,
    color: undefined,
  }),
});
interface Emit {
  (e: "update:isDialogVisible", value: boolean): void;
  (e: "submit", value: Schema): void;
}
const emit = defineEmits<Emit>();

const isDialogVisible = ref(props.isDialogVisible);
const state = ref<Partial<Schema>>(structuredClone(toRaw(props.vehicle)));
watch(props, () => {
  state.value = structuredClone(toRaw(props.vehicle));
  isDialogVisible.value = props.isDialogVisible;
});

async function onSubmit(event: FormSubmitEvent<Schema>) {
  const { data } = event;

  const response = await $api<ApiResponse<Schema>>("/api/vehicle.php", {
    method: props.vehicle.id ? "PUT" : "POST",
    query: props.vehicle.id ? { id: props.vehicle.id } : undefined,
    body: JSON.stringify(data),
  });
  if (response) {
    toast.add({
      title: response.code,
      description: response.message,
      icon: response.status ? "i-lucide-check" : "i-lucide-x",
      color: response.status ? "success" : "error",
    });
    if (!response.status) {
      return;
    }
  }

  emit("update:isDialogVisible", false);
  emit("submit", data);
}

const resetForm = () => {
  emit("update:isDialogVisible", false);
  state.value = structuredClone(toRaw(props.vehicle));
};
</script>

<template>
  <UModal
    v-model:open="isDialogVisible"
    :title="props.vehicle?.id ? t('edit') : t('add')"
    @update:open="(value: boolean) => emit('update:isDialogVisible', value)"
  >
    <template #body>
      <UForm
        :schema="vehicleSchema"
        :state="state"
        class="space-y-4"
        @submit="onSubmit"
      >
        <div class="flex flex-row gap-4 mb-2">
          <div class=" basis-1/3">
            <UFormField
              :label="t('table.plateNo')"
              name="licensePlate"
            >
              <UInput
                v-model="state.licensePlate"
                class="w-full"
              />
            </UFormField>
          </div>
          <div class=" basis-2/3">
            <UFormField
              :label="t('vehicle.province')"
              name="provinceId"
            >
              <USelectMenu
                v-model="state.provinceId"
                class="w-full"
                :items="optionsProvince"
                value-key="value"
                label-key="label"
              />
            </UFormField>
          </div>
        </div>
        <UFormField
          :label="t('vehicle.description')"
          name="description"
        >
          <UTextarea
            v-model="state.description"
            class="w-full"
          />
        </UFormField>

        <UFormField
          :label="t('table.organization')"
          name="organizationId"
        >
          <USelectMenu
            v-model="state.organizationId"
            class="w-full"
            :items="optionsEngine"
            value-key="value"
            label-key="label"
          />
        </UFormField>
        <div class="flex flex-row gap-4 mb-2">
          <div class=" basis-2/3">
            <UFormField
              :label="t('table.model')"
              name="modelId"
            >
              <USelectMenu
                v-model="state.modelId"
                class="w-full"
                :items="optionsVehicleModel"
                value-key="value"
                label-key="label"
              />
            </UFormField>
          </div>
          <div class=" basis-1/3">
            <UFormField
              :label="t('vehicle.color')"
              name="color"
            >
              <USelectMenu
                v-model="state.color"
                class="w-full"
                :items="optionsColor"
                value-key="value"
                label-key="label"
              />
            </UFormField>
          </div>
        </div>
        <div class="flex justify-end gap-2">
          <UButton
            :label="t('cancel')"
            color="neutral"
            variant="subtle"
            @click="resetForm"
          />
          <UButton
            :label="t('save')"
            color="primary"
            variant="solid"
            type="submit"
            loading-auto
          />
        </div>
      </UForm>
    </template>
  </UModal>
</template>
