<script lang="ts" setup>
import { useProfileStore } from "~/stores/profile";
import type { OidcToken, Profile, ResponseSignup } from "~/types";

definePageMeta({
  layout: "auth",
});

const router = useRouter();
const { handleCallback, getUser } = useAuth();
const { setProfile, setToken } = useProfileStore();
const { initialOptions } = useOptionsStore();
const toast = useToast();

onMounted(async () => {
  try {
    // Handle the authentication callback
    await handleCallback();
    // Redirect to the home page after successful login

    const token = await getUser();
    if (token) {
      const oidcToken: OidcToken = {
        access_token: token.access_token,
        expires_at: token.expires_at,
        id_token: token.id_token,
        refresh_token: token.refresh_token,
        scope: token.scope,
        token_type: token.token_type,
        session_state: token.session_state,
      };

      setToken(oidcToken);
    }

    const { data, error } = await useApi<ResponseSignup>("/api/signin.php", {
      method: "GET",
    });

    if (error.value) {
      toast.add({
        title: "Uh oh! Something went wrong.",
        description: String(error.value) || "Please try again later.",
        icon: "i-lucide-x",
        progress: false,
        color: "error",
      });
      return;
    }

    if (data.value?.details) {
      const profile = data.value.details as Profile;
      setProfile(profile);
      await callOnce(() => initialOptions());
    }
    await router.push("/dashboard");
  }
  catch (error) {
    console.error("Authentication callback failed:", error);
    // Optionally, redirect to an error page or show a message
    await router.push("/");
  }
});
</script>

<template>
  <section class="dots-container">
    <div class="dot" />
    <div class="dot" />
    <div class="dot" />
    <div class="dot" />
    <div class="dot" />
  </section>
</template>

<style scoped>
/* From Uiverse.io by adamgiebl */
.dots-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
}

.dot {
  height: 20px;
  width: 20px;
  margin-right: 10px;
  border-radius: 10px;
  background-color: #b3d4fc;
  animation: pulse 1.5s infinite ease-in-out;
}

.dot:last-child {
  margin-right: 0;
}

.dot:nth-child(1) {
  animation-delay: -0.3s;
}

.dot:nth-child(2) {
  animation-delay: -0.1s;
}

.dot:nth-child(3) {
  animation-delay: 0.1s;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    background-color: #b3d4fc;
    box-shadow: 0 0 0 0 rgba(178, 212, 252, 0.7);
  }

  50% {
    transform: scale(1.2);
    background-color: #6793fb;
    box-shadow: 0 0 0 10px rgba(178, 212, 252, 0);
  }

  100% {
    transform: scale(0.8);
    background-color: #b3d4fc;
    box-shadow: 0 0 0 0 rgba(178, 212, 252, 0.7);
  }
}
</style>
