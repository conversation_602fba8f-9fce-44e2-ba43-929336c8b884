<script setup lang="ts">
import type { Vehicle } from "~/types";

const { t } = useI18n();

interface Emit {
  (e: "update:isDialogVisible", value: boolean): void;
}
interface Props {
  vehicle?: Vehicle;
  isDialogVisible: boolean;
}

const emit = defineEmits<Emit>();
const props = defineProps<Props>();

const isDialogVisible = ref(props.isDialogVisible);
const vehicle = ref<Vehicle | undefined>(props.vehicle);

watch(props, () => {
  isDialogVisible.value = props.isDialogVisible;
  vehicle.value = props.vehicle;
});

const checkColor = (color?: string) => {
  return color === "WHITE" ? "neutral" : color;
};
</script>

<template>
  <UModal
    v-model:open="isDialogVisible"
    :title="t('vehicle.title')"
    :width="600"
    :class="['max-w-2xl']"
    @update:open="(value) => emit('update:isDialogVisible', value)"
  >
    <template #body>
      <div class="space-y-2 items-center">
        <div class="flex flex-row gap-4 mb-2">
          <div class="font-bold basis-1/3">
            {{ t("vehicle.plateNo") }}
          </div>
          <div class="text-muted basis-2/3">
            {{ vehicle?.licensePlate }}
          </div>
        </div>

        <USeparator />
        <div class="flex flex-row gap-4 mb-2">
          <div class="font-bold basis-1/3">
            {{ t("vehicle.province") }}
          </div>
          <div class="text-muted basis-2/3">
            {{ vehicle?.provinceName }}
          </div>
        </div>
        <USeparator />
        <div class="flex flex-row gap-4 mb-2">
          <div class="font-bold basis-1/3">
            {{ t("vehicle.brand") }}
          </div>
          <div class="text-muted basis-2/3">
            {{ vehicle?.model?.brand }}
          </div>
        </div>
        <USeparator />
        <div class="flex flex-row gap-4 mb-2">
          <div class="font-bold basis-1/3">
            {{ t("vehicle.color") }}
          </div>
          <div class="text-muted basis-2/3">
            <UBadge
              :color="checkColor(vehicle?.color)"
              variant="subtle"
            >
              {{ vehicle?.color }}
            </UBadge>
          </div>
        </div>
        <USeparator />
        <div class="flex flex-row gap-4 mb-2">
          <div class="font-bold basis-1/3">
            {{ t("vehicle.type") }}
          </div>
          <div class="text-muted basis-2/3">
            {{ vehicle?.model?.type }}
          </div>
        </div>
        <USeparator />
        <div class="flex flex-row gap-4 mb-2">
          <div class="font-bold basis-1/3">
            {{ t("table.engine") }}
          </div>
          <div class="text-muted basis-2/3">
            {{ vehicle?.model?.engine }}
          </div>
        </div>
        <USeparator />
        <div class="flex flex-row gap-4 mb-2">
          <div class="font-bold basis-1/3">
            {{ t("table.seat") }}
          </div>
          <div class="text-muted basis-2/3">
            {{ vehicle?.model?.seat }}
          </div>
        </div>
        <USeparator />
        <div class="flex flex-row gap-4 mb-2">
          <div class="font-bold basis-1/3">
            {{ t("table.year") }}
          </div>
          <div class="text-muted basis-2/3">
            {{ vehicle?.model?.year }}
          </div>
        </div>
        <USeparator />
        <div class="flex flex-row gap-4 mb-2">
          <div class="font-bold basis-1/3">
            {{ t("table.organization") }}
          </div>
          <div class="text-muted basis-2/3">
            {{ vehicle?.organizationName }}
          </div>
        </div>
        <USeparator />
        <div class="flex flex-row gap-4 mb-2">
          <div class="font-bold basis-1/3">
            {{ t("vehicle.description") }}
          </div>
          <div class="text-muted basis-2/3">
            {{ vehicle?.description }}
          </div>
        </div>
      </div>
    </template>
  </UModal>
</template>
