// auth.d.ts
declare module "#auth-utils" {
  interface user {
    id: number;
    fullname: string;
    jobTitle: string;
    email: string;
    phone: string;
    organizationId: string;
    organzationName: string;
    username: string;
    signatureLevel: number;
    permissionId: string;
    permissionName: string;
    permission: {
      all: number;
    };
    createDate: string;
  }

  interface userSession {
    "access_token": string;
    "expires_in": number;
    "refresh_expires_in": number;
    "refresh_token": string;
    "token_type": string;
    "id_token": string;
    "not-before-policy": number;
    "session_state": string;
    "scope": string;
  }

  // interface SecureSessionData {
  //   // Add your own fields
  // }
}

export {};
