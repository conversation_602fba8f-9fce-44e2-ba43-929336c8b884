<script setup lang="ts">
import type {
  DropdownMenuItem,
  NavigationMenuItem,
} from "@nuxt/ui";

const { t } = useI18n();
const store = useProfileStore();
const { profile } = storeToRefs(store);

const route = useRoute();

const links = computed<NavigationMenuItem[]>(() => [
  {
    label: t("nav.main"),
    icon: "i-lucide-home",
    to: "/dashboard",
  },
  {
    label: t("nav.booking"),
    icon: "i-lucide-car",
    to: "/vehicles",
  },
  {
    label: t("nav.myBooking"),
    icon: "i-lucide-clipboard-list",
    type: "trigger",
    active: route.path.startsWith("/bookings"),
    children: [
      {
        label: t("statusOptions.pending"),
        to: "/bookings/0",
      },
      {
        label: t("statusOptions.approved"),
        to: "/bookings/1",
      },
      {
        label: t("statusOptions.notAllow"),
        to: "/bookings/2",
      },
      {
        label: t("statusOptions.cancelByCustomer"),
        to: "/bookings/3",
      },
      {
        label: t("statusOptions.cancelByStaff"),
        to: "/bookings/4",
      },
    ],
  },
  {
    label: t("nav.approve"),
    icon: "i-lucide-check-circle",
    to: "/approve",
  },
  // {
  //   label: t("nav.report"),
  //   icon: "i-lucide-bar-chart-2",
  //   to: "/report",
  // },
  {
    label: t("nav.setting"),
    icon: "i-lucide-settings",
    to: "/settings/organization",
  },
]);

const itemsDropdown = computed<DropdownMenuItem[]>(() => [
  {
    label: t("nav.profile"),
    icon: "i-lucide-user",
    to: "/users/members/edit/1",
  },
  {
    label: t("nav.logout"),
    icon: "i-lucide-log-out",
    onSelect: logOut,
  },
]);

const logOut = async () => {
  try {
    await useApi("/api/signout.php", {
      method: "GET",
    });
    await navigateTo("/");
  }
  catch (error) {
    console.error("Logout failed:", error);
  }
};
</script>

<template>
  <UDashboardGroup unit="rem">
    <UDashboardPanel
      id="navbar"
      :ui="{ body: 'lg:py-0 sm:gap-1' }"
    >
      <template #header>
        <UDashboardNavbar :ui="{ toggle: 'hidden' }">
          <template #left>
            <div class="flex items-center gap-2 ">
              <img
                src="~/assets/images/logo.png"
                alt="Logo"
                class="mx-auto w-10 h-10"
              >
              <p class="text-2xl md:inline-flex hidden overflow-hidden text-ellipsis">
                ระบบขอใช้ยานพาหนะสถานีอนามัยเฉลิมพระเกียรติฯ และโรงพยาบาลส่งเสริมสุขภาพตำบล
              </p>
            </div>
          </template>
          <template #right>
            <div class="flex flex-row space-x-4 items-center">
              <UColorModeSwitch />

              <LanguageSwitcher />
              <UDropdownMenu
                :items="itemsDropdown"
                :content="{
                  align: 'end',
                  side: 'bottom',
                  sideOffset: 8,
                }"
                :ui="{
                  content: 'w-48',
                }"
              >
                <UButtonGroup class="flex items-center gap-2">
                  <UAvatar
                    :alt="profile?.fullname"
                    size="lg"
                  />
                  <UUser
                    :name="profile?.fullname"
                    :description="profile?.jobTitle"
                    :chip="{
                      color: 'primary',
                      position: 'top-right',
                    }"
                    class="md:inline-flex hidden"
                  />
                  <UIcon
                    name="i-lucide-chevron-down"
                    class="text-muted"
                  />
                </UButtonGroup>
              </UDropdownMenu>
            </div>
          </template>
        </UDashboardNavbar>

        <UDashboardNavbar
          class="h-10"
          :ui="{ toggle: 'hidden' }"
        >
          <template #left>
            <UNavigationMenu
              content-orientation="vertical"
              :items="links"
              class=" w-full"
            />
          </template>
        </UDashboardNavbar>
      </template>

      <template #body>
        <slot />
      </template>

      <template #footer>
        <USeparator class="h-px" />

        <span class="text-muted text-sm p-2 truncate">
          Copyright © {{ new Date().getFullYear() }}, รุ่น 6.9.0 Power by
          ศูนย์เทคโนโลยีสารสนเทศและการสื่อสาร องค์การบริหารส่วนจังหวัดชลบุรี
        </span>
      </template>
    </UDashboardPanel>
  </UDashboardGroup>
</template>
