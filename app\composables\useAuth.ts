import { UserManager, WebStorageStateStore } from "oidc-client-ts";
import type { UserManagerSettings } from "oidc-client-ts";

export const useAuth = () => {
  const config = useRuntimeConfig();

  const oidcConfig: UserManagerSettings = {
    authority: config.public.apiBaseRealm + "/realms/supercar",
    client_id: config.public.clientId as string,
    redirect_uri: `${window.location.origin}/callback`,
    response_type: "code",
    scope: "openid profile email",
    post_logout_redirect_uri: `${window.location.origin}/`,
    userStore: new WebStorageStateStore({
      store: window.localStorage,
    }),
  };

  const userManager: UserManager = new UserManager(oidcConfig);

  const login = async () => userManager.signinRedirect();
  const logout = async () => userManager.signoutRedirect();
  const getUser = async () => userManager.getUser();
  const handleCallback = async () => userManager.signinRedirectCallback();

  const isAuthenticated = async () => {
    const user = await getUser();
    return !!user && !user.expired;
  };

  return {
    login,
    logout,
    getUser,
    handleCallback,
    isAuthenticated,
  };
};
