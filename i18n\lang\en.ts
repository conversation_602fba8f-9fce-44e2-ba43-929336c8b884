export default {
  nav: {
    main: "Home",
    booking: "Book a vehicle",
    myBooking: "My Bookings",

    users: {
      main: "Users",
      all: "Member List",
      approvel: "Approval Groups",
      role: "Roles",
    },
    approve: "Approval",
    report: "Report",
    setting: "Setting",
    profile: "Profile",
    logout: "Logout",
    login: "Login",
  },
  navSetting: {
    site: "Site Setting",
    organization: "Organization Setting",
    website: "Website Setting",
    login: "Login page",
    email: "Email Setting",
    notification: "Notification Setting",
    line: "Line Setting",
    telegram: "Telegram Setting",
    api: "API Setting",
    sms: "SMS Setting",
    module: "Module",
    language: "Language",
    department: "Department Setting",
    cookie: "Cookie Policy",
    history: "Usage History",
    vehicle: {
      title: "Vehicle Management",
      settings: "Settings",
      list: "List of vehicles",
      accessories: "Accessories",
      types: "Vehicle Types",
      brand: "Vehicle Brands",
    },
  },

  formSite: {
    title: "General",
    subtitle: "Member",
    siteName: "Site Name",
    siteDescription: "Site Description",
    siteLogo: "Site Logo",
    siteTimezone: "Site Timezone",
    siteColor: "Site Color",
    siteColorHeader: "Site Header Color",
    siteTextColor: "Site Text Color",

    forgetPassword: "Forget Password",
    register: "Register",
    newRegister: "New Register",
    loginBy: "Login by",
    sendEmailWelcome: "Send welcome message to new members",
    department: "Department",
    googleClientId: "Google Client ID",
    facebookAppId: "Facebook App ID",
    demoMode: "Demo Mode",
  },

  formSearch: {
    search: "Search",
    department: "Department",
    show: "Show",
    status: "Status",
    type: "Type",
    brand: "Brand",
    vehicle: "Vehicle",
    startDate: "Start Date",
    chauffeur: "Chauffeur",
    organization: "Organization",
  },

  formUser: {
    main: "Editing your account",
    title: "Login Information",
    email: "Email",
    password: "Password",
    confirmPassword: "Confirm Password",
    phone: "Phone",
    idCard: "Identification No.",
    secondTitle: "Details of User",
    nameSurname: "Name Surname",
    sex: "Sex",
    sexOptions: {
      notSet: "Not Specified",
      male: "Male",
      female: "Female",
    },
    department: "Department",
    address: "Address",
    country: "Country",
    province: "Province",
    zipcode: "Zip Code",
    avatar: "Avatar",
    thirdTitle: "Other Details",
    memberStatus: "Member Status",
    permission: "Permission",
    status: "Status",
    role: "Role",
    approvel: "Approval Group",
    singnatory: "Signatory",
    username: "Username",
    jobTitle: "Job Title",
    notificationCount: "Notification Count",
  },

  formEmail: {
    title: "General",
    noreply: "No Reply Email",
    encodeing: "Email Encoding",
    mailProgram: "Mail Program",
    secondsTitle: "Mail server settings",
    mailServer: "Mail Server",
    port: "Port",
    authenticationRequired: "Authentication Required",
    sslSupport: "SSL Support",
  },

  pageVehicle: {
    title: "List of Vehicle",
    booking: "Book a Vehicle",
    detail: "Detail",
    phone: "Phone",
    idCard: "Identification No.",
    vehicle: "Vehicle",
    startDate: "Start Date",
  },

  pageBooking: {
    title: "Book a Vehicle",
    usageDetails: "Usage Details",
    vehicle: "Vehicle",
    type: "Type",
    brand: "Brand",
    chauffeur: "Chauffeur",
    date: "Date",
    numberOfSeats: "Number of Seats",
    contactName: "Contact Name",
    contactPhone: "Contact Phone",
    contactEmail: "Contact Email",
  },

  pageMyBooking: {
    title: "My Bookings",
  },

  pageApproval: {
    title: "Approval Groups",
  },

  pageMember: {
    title: "Member List",
  },

  pageRole: {
    title: "List of Roles",
  },

  pageReport: {
    title: "Report",
  },

  pageNotification: {
    title: "Notification",
    email: "Email Notification",
  },

  table: {
    vehicle: "Vehicle",
    numberOfSeats: "Number of Seats",
    footer: "Page {page} of {total} total pages, total {totalItems} items",

    usageDetails: "Usage Details",
    status: "Status",
    reason: "Reason",

    username: "Username",
    phone: "Phone",
    createAt: "createAt",
    createBy: "createAt By",
    memberStatus: "Member Status",
    contactName: "Contact Name",

    chauffeur: "Chauffeur",
    agencyName: "Organization Name",

    roleName: "Role Name",

    groupName: "Group Name",
    sequence: "Sequence",
    organization: "Organization",
    dateReservation: "Date Reservation",
    passengers: "Passengers",
    driverName: "Driver Name",

    name: "Name",
    brand: "Brand",
    model: "Model",
    engine: "Engine",
    seat: "Seat",
    year: "Year",
    type: "Type",
    plateNo: "Plate No.",
    publish: "Publish",

    bookingStart: "Booking Start and End",
    address: "Address",
    department: "Department",
  },

  dashboard: {
    title: "Dashboard",
    stats: {
      pending: "Pending Bookings",
      approved: "Approved Bookings",
      rejected: "Rejected Bookings",
      vehicle: "Total Vehicles",
    },
  },

  booking: {
    title: "Booking Details",
    usageDetails: "Usage Details",
    numberOfTravelers: "Number of Travelers",
    contactName: "Contact name",
    phone: "Phone",
    vehicle: "Vehicle",
    type: "Type",
    brand: "Brand",
    chauffeur: "Chauffeur",
    date: "Date",
    status: "Status",
    beginDate: "Begin Date",
    beginTime: "Begin Time",
    endDate: "End Date",
    endTime: "End Time",
    accessories: "Accessories",
    other: "Other Details",
    fullOil: "Full Tank",
    soundSystem: "Sound System",
    reason: "Reason",
    remark: "Remark",
    project: "Project",
    description: "Description",
    organization: "Organization",
    passengers: "Passengers",
    driverName: "Driver Name",
  },
  vehicle: {
    title: "Details of Vehicle",
    plateNo: "Vehicle No.",
    province: "Province",
    description: "Description",
    type: "Type",
    brand: "Brand",
    color: "Color",
    numberOfSeats: "Number of Seats",
  },

  active: "Active",
  inactive: "Inactive",
  passlogin: "Can login",
  notpasslogin: "Cannot login (wait to check from the staff)",
  username: "Username",
  password: "Password",
  email: "Email",
  phone: "Phone",
  idCard: "Identification No.",

  view: "View",
  save: "Save",
  create: "Create",
  cancel: "Cancel",
  delete: "Delete",
  edit: "Edit",
  add: "Add",
  update: "Update",
  deleteImage: "Delete Image",
  approve: "Approve",
  all: "All",
  detail: "Detail",
  cancelByCustomer: "Cancelled by Customer",
  cancelByStaff: "Cancelled by Staff",

  confirmDelete: "Are you sure you want to delete this item?",
  confirmUpdate: "Are you sure you want to update this item?",
  confirm: "Confirm",
  success: "Success",
  error: "Error",
  loading: "Loading...",
  notFound: "Not Found",
  noData: "No Data Available",

  location: "Location",
  departmentName: "Department Name",
  organization: "Organization",
  permissionRole: "Permission Role",

  statusOptions: {
    all: "All",
    pending: "Pending",
    approved: "Approved",
    notAllow: "Not Allowed",
    cancelByCustomer: "Cancelled by Customer",
    cancelByStaff: "Cancelled by Staff",
  },

  permissionOptions: {
    config: "Can configure the system",
    history: "Able to view system usage history",
    manage: "Can manage the vehicles",
  },

  mailProgram: {
    default: "Send with PHP",
    smtp: "Send with PHPMailer+SMTP (recommended)",
    phpmailer: "Send with PHPMailer+PHP mail",
  },

  enableOptions: {
    enable: "Enable",
    disable: "Disable",
  },

  sslOptions: {
    clearText: "Clear Text",
    ssl: "Server using a secure connection (SSL)",
  },

  publishOptions: {
    published: "Published",
    unpublished: "Unpublished",
  },
};
