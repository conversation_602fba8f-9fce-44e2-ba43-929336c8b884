export abstract class ApiError extends Error {
  statusCode?: number;

  constructor(code: string, message: string, statusCode?: number) {
    super(message);
    this.name = code;
    this.message = message;
    this.statusCode = statusCode;
  }
}

export class ResponseApiError extends ApiError {
  constructor(code = "UNKNOWN_ERROR", message = "An error occurred", statusCode?: number) {
    super(code, message, statusCode);
  }
}
