<script setup lang="ts">
import type { TableColumn } from "@nuxt/ui";
import { getPaginationRowModel } from "@tanstack/vue-table";
import type { Accessories } from "~/types";

definePageMeta({
  middleware: ["authenticated"],
});

const { t } = useI18n();
const UButton = resolveComponent("UButton");

const isDialogVisible = ref(false);

const data = ref<Accessories[]>([
  {
    id: 1,
    name: "john_doe",
    status: true,
    createAt: "2023-01-01",
    createBy: "admin",
  },
  {
    id: 2,
    name: "jane_smith",
    status: false,
    createAt: "2023-01-02",
    createBy: "admin",
  },
]);
const table = useTemplateRef("table");

const pagination = ref({
  pageIndex: 0,
  pageSize: 10,
});
const columns = computed<TableColumn<Accessories>[]>(() => [
  {
    accessorKey: "name",
    header: () => h("span", t("table.name")),
  },
  {
    accessorKey: "status",
    header: () => h("div", t("table.status")),
  },
  {
    accessorKey: "createAt",
    header: () => h("div", t("table.createAt")),
  },
  {
    accessorKey: "createBy",
    header: () => h("div", t("table.createBy")),
  },
  {
    id: "actions",
    cell: () => {
      return h("div", { class: "flex justify-end gap-2" }, [
        h(UButton, {
          icon: "i-lucide-edit",
          label: t("edit"),
          color: "success",
          class: "",
          onClick: () => {},
        }),
        h(UButton, {
          icon: "i-lucide-trash",
          label: t("delete"),
          color: "error",
          onClick: () => {
            // Handle delete action
          },
        }),
      ]);
    },
  },
]);
</script>

<template>
  <div class="flex items-center gap-2 mb-4">
    <UIcon
      name="i-lucide-package"
      class="size-5"
    />
    <h1 class="text-xl">
      {{ t("navSetting.vehicle.accessories") }}
    </h1>

    <UButton
      form="memberForm"
      icon="i-lucide-plus"
      :label="t('add')"
      color="info"
      type="submit"
      class="w-fit lg:ms-auto"
      @click="isDialogVisible = true"
    />
  </div>

  <div class="w-full z-0">
    <UTable
      ref="table"
      v-model:pagination="pagination"
      :data="data"
      :columns="columns"
      :pagination-options="{
        getPaginationRowModel: getPaginationRowModel(),
      }"
    />

    <div
      class="flex items-center justify-between gap-3 border-t border-default pt-4 mt-auto"
    >
      <div class="text-sm text-muted">
        {{
          t("table.footer", {
            page: (table?.tableApi?.getState().pagination.pageIndex || 0) + 1,
            total: table?.tableApi?.getPageCount(),
            totalItems: table?.tableApi?.getFilteredRowModel().rows.length || 0,
          })
        }}
      </div>

      <div class="flex items-center gap-1.5">
        <UPagination
          :default-page="
            (table?.tableApi?.getState().pagination.pageIndex ?? 0) + 1
          "
          :items-per-page="table?.tableApi?.getState().pagination.pageSize"
          :total="table?.tableApi?.getFilteredRowModel().rows.length"
          @update:page="(p: number) => table?.tableApi?.setPageIndex(p - 1)"
        />
      </div>
    </div>
  </div>

  <AccessoriesDialog
    v-model:is-dialog-visible="isDialogVisible"
    :accessories="undefined"
    @submit="
      () => {
        // Handle submit logic
        isDialogVisible = false;
      }
    "
  />
</template>
