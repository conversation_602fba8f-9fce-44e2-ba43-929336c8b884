<script setup lang="ts">
import type { TableColumn } from "@nuxt/ui";
import type { Model } from "~/types";

definePageMeta({
  middleware: ["authenticated"],
});

const toast = useToast();
const { t } = useI18n();
const UButton = resolveComponent("UButton");
const { initialOptions } = useOptionsStore();
const optionsStore = useOptionsStore();
const {
  optionsPerPage,
} = storeToRefs(optionsStore);

const isDialogVisible = ref(false);
const isDeleteDialogVisible = ref(false);

const search = ref("");
const page = ref(1);
const totalRecords = ref(0);
const totalPages = ref(0);
const perPages = ref(10);
const editedItem = ref<Model | undefined>(undefined);
const deletePath = ref<string | undefined>(undefined);

const columns = computed<TableColumn<Model>[]>(() => [
  {
    accessorKey: "brand",
    header: () => h("span", t("table.brand")),
  },
  {
    accessorKey: "model",
    header: () => h("div", t("table.model")),
  },
  {
    accessorKey: "engine",
    header: () => h("div", t("table.engine")),
  },
  {
    accessorKey: "seat",
    header: () => h("div", t("table.seat")),
  },
  {
    accessorKey: "year",
    header: () => h("div", t("table.year")),
  },
  // {
  //   accessorKey: "createAt",
  //   header: () => h("div", t("table.createAt")),
  // },
  // {
  //   accessorKey: "createBy",
  //   header: () => h("div", t("table.createBy")),
  // },
  {
    id: "actions",
    cell: ({ row }) => {
      return h("div", { class: "flex justify-end gap-2" }, [
        h(UButton, {
          icon: "i-lucide-edit",
          label: t("edit"),
          color: "success",
          class: "",
          onClick: () => {
            editedItem.value = row.original;
            isDialogVisible.value = true;
          },
        }),
        h(UButton, {
          icon: "i-lucide-trash",
          label: t("delete"),
          color: "error",
          onClick: () => {
            deletePath.value = `/api/model.php?id=${row.original.id}`;
            isDeleteDialogVisible.value = true;
          },
        }),
      ]);
    },
  },
]);

await callOnce(() => initialOptions());

const { data, status, refresh } = await useApi<Model[]>(`/api/model.php`, {
  query: {
    search: search,
    page: page,
    perPages: perPages,
  },

  transform: (response) => {
    if (!response?.status) {
      toast.add({
        title: response.code,
        description: response.message,
        icon: "i-lucide-x",
        progress: false,
        color: "warning",
      });
      return [];
    }
    page.value = response?.pagination?.page;
    perPages.value = response?.pagination?.perPages;
    totalRecords.value = response?.pagination?.totalRecords;
    totalPages.value = response?.pagination?.totalPages;
    return response?.details?.map(item => item) as Model[];
  },
});

const onSubmit = () => {
  editedItem.value = undefined;
  deletePath.value = undefined;
  isDialogVisible.value = false;
  isDeleteDialogVisible.value = false;
  refresh();
};
</script>

<template>
  <div class="flex items-center gap-2 mb-4">
    <UIcon
      name="i-tabler-brand-carbon"
      class="size-5"
    />
    <h1 class="text-xl">
      {{ t("navSetting.vehicle.brand") }}
    </h1>

    <UButton
      form="memberForm"
      icon="i-lucide-plus"
      :label="t('add')"
      color="info"
      type="submit"
      class="w-fit lg:ms-auto"
      @click="isDialogVisible = true"
    />
  </div>

  <div class="flex flex-col gap-3 md:flex-row md:items-end md:justify-between md:text-end">
    <div class="flex w-full flex-row gap-1.5 sm:flex-row sm:items-end">
      <UFormField
        :label="t('formSearch.search')"
        name="search"
        class="w-full sm:w-72"
      >
        <UInput
          v-model="search"
          class="w-full"
        />
      </UFormField>

      <UButton class="self-end sm:self-auto">
        {{ t("formSearch.search") }}
      </UButton>
    </div>
    <div class="flex w-full flex-wrap gap-1.5 justify-end md:w-auto lg:flex-nowrap">
      <UFormField
        :label="t('formSearch.show')"
        name="pageSize"
        class="w-20 shrink-0"
      >
        <USelect
          v-model="perPages"
          :items="optionsPerPage"
          class="w-full"
        />
      </UFormField>
    </div>
  </div>

  <div class="w-full z-0">
    <UTable
      ref="table"
      :loading="status === 'pending'"
      :data="data"
      :columns="columns"
    />

    <CustomPagination
      v-model="page"
      :total-pages="totalPages"
      :total-records="totalRecords"
      :per-pages="perPages"
      class="mt-4"
    />
  </div>

  <ModelDialog
    v-model:is-dialog-visible="isDialogVisible"
    :model="editedItem"
    @submit="onSubmit"
  />

  <DeleteDialog
    v-model:is-dialog-visible="isDeleteDialogVisible"
    :url="deletePath"
    @submit="onSubmit"
  />
</template>
