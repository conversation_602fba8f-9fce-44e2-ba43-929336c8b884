<script setup lang="ts">
import { storeToRefs } from "pinia";
import type { TableColumn } from "@nuxt/ui";
import { h, resolveComponent } from "vue";
import { useOptionsStore } from "~/stores/options";
import type { Vehicle } from "~/types";

definePageMeta({
  middleware: ["authenticated"],
});

const toast = useToast();
const { t } = useI18n();
const optionsStore = useOptionsStore();
const {
  optionsType,
  optionsBrand,
  optionsEnable,
  optionsPerPage,
  optionsPublish,
} = storeToRefs(optionsStore);
const UBadge = resolveComponent("UBadge");
const UButton = resolveComponent("UButton");

const isDialogVisible = ref(false);
const isDialogDetailVisible = ref(false);
const isDeleteDialogVisible = ref(false);
const editedItem = ref<Vehicle | undefined>(undefined);
const deletePath = ref<string | undefined>(undefined);

const columns = computed<TableColumn<Vehicle>[]>(() => [
  {
    accessorKey: "licensePlate",
    header: () => h("span", t("table.plateNo")),
    cell: ({ row }) => {
      return h("div", { class: "flex items-center gap-3" }, [
        // h(UAvatar, {
        //   ...row.original.image,
        //   size: "lg",
        // }),
        h("div", undefined, [
          h("p", { class: "font-medium text-highlighted" }, row.original.licensePlate),
          h("p", { class: "" }, `${row.original.provinceName}`),
        ]),
      ]);
    },
  },
  {
    accessorKey: "model.brand",
    header: () => h("span", t("table.brand")),
    cell: ({ row }) => {
      return h("div", undefined, [
        h("p", { class: "" }, `${row.original?.model?.brand}`),
        h(UBadge, { color: row.original.color === "WHITE" ? "neutral" : row.original.color, variant: "subtle" }, [
          h("span", undefined, row.original.color),
        ]),

      ]);
    },
  },
  {
    accessorKey: "model.type",
    header: () => h("span", t("table.type")),
  },
  {
    accessorKey: "model.engine",
    header: () => h("span", t("table.engine")),
  },
  {
    accessorKey: "model.seat",
    header: () => h("span", t("table.numberOfSeats")),
  },
  {
    accessorKey: "model.year",
    header: () => h("span", t("table.year")),
  },
  {
    accessorKey: "organizationName",
    header: () => h("span", t("table.organization")),
  },
  // {
  //   accessorKey: "status",
  //   header: () => h("span", t("table.status")),
  // },
  {
    id: "actions",
    cell: ({ row }) => {
      return h("div", { class: "flex justify-end gap-2" }, [
        h(UButton, {
          icon: "i-lucide-eye",
          label: t("detail"),
          color: "info",
          class: "",
          onClick: () => {
            editedItem.value = row.original;
            isDialogDetailVisible.value = true;
          },
        }),
        h(UButton, {
          icon: "i-lucide-edit",
          label: t("edit"),
          color: "success",
          class: "",
          onClick: () => {
            editedItem.value = row.original;
            isDialogVisible.value = true;
          },
        }),
        h(UButton, {
          icon: "i-lucide-trash",
          label: t("delete"),
          color: "error",
          onClick: () => {
            deletePath.value = `/api/vehicle.php?id=${row.original.id}`;
            isDeleteDialogVisible.value = true;
          },
        }),
      ]);
    },
  },
]);

const search = ref("");
const page = ref(1);
const perPages = ref(10);
const totalRecords = ref(0);
const totalPages = ref(0);

const selectTypeVehicle = ref("it");
const selectBrandVehicle = ref("active");
const selectPublish = ref("published");
const selectStatus = ref("active");

const { data, status, refresh } = await useApi<Vehicle[]>(`/api/vehicle.php`, {
  query: {
    search: search,
    page: page,
    perPages: perPages,
  },
  transform: (response) => {
    if (!response?.status) {
      toast.add({
        title: response.code,
        description: response.message,
        icon: "i-lucide-x",
        progress: false,
        color: "warning",
      });
      return [];
    }

    page.value = response?.pagination?.page;
    perPages.value = response?.pagination?.perPages;
    totalRecords.value = response?.pagination?.totalRecords;
    totalPages.value = response?.pagination?.totalPages;
    return response.details?.map(item => item) || [];
  },

});

const onSubmit = () => {
  editedItem.value = undefined;
  deletePath.value = undefined;
  isDialogVisible.value = false;
  isDeleteDialogVisible.value = false;
  refresh();
};
</script>

<template>
  <div class="flex items-center gap-2 mb-4">
    <UIcon
      name="i-lucide-car"
      class="size-5"
    />
    <h1 class="text-xl">
      {{ t("pageVehicle.title") }}
    </h1>

    <UButton
      form="memberForm"
      icon="i-lucide-plus"
      :label="t('add')"
      color="info"
      type="submit"
      class="w-fit lg:ms-auto"
      @click="isDialogVisible = true"
    />
  </div>

  <div class="flex flex-col gap-3 md:flex-row md:items-end md:justify-between md:text-end">
    <div class="flex w-full flex-row gap-1.5 sm:flex-row sm:items-end">
      <UFormField
        :label="t('formSearch.search')"
        name="search"
        class="w-full sm:w-72"
      >
        <UInput
          v-model="search"
          class="w-full"
        />
      </UFormField>

      <UButton class="self-end sm:self-auto">
        {{ t("formSearch.search") }}
      </UButton>
    </div>
    <div class="flex w-full flex-wrap gap-1.5 justify-end md:w-auto lg:flex-nowrap">
      <UFormField
        :label="t('formSearch.type')"
        name="vehicle"
        class="w-full sm:w-40 shrink-0"
      >
        <USelect
          v-model="selectTypeVehicle"
          :items="optionsType"
          value-key="value"
          class="w-full"
        />
      </UFormField>

      <UFormField
        :label="t('formSearch.brand')"
        name="brandVehicle"
        class="w-full sm:w-40 shrink-0"
      >
        <USelect
          v-model="selectBrandVehicle"
          :items="optionsBrand"
          value-key="value"
          class="w-full"
        />
      </UFormField>

      <UFormField
        :label="t('table.publish')"
        name="publish"
        class="w-full sm:w-40 shrink-0"
      >
        <USelect
          v-model="selectPublish"
          :items="optionsPublish"
          value-key="value"
          class="w-full"
        />
      </UFormField>

      <UFormField
        :label="t('table.status')"
        name="status"
        class="w-full sm:w-40 shrink-0"
      >
        <USelect
          v-model="selectStatus"
          :items="optionsEnable"
          value-key="value"
          class="w-full"
        />
      </UFormField>

      <UFormField
        :label="t('formSearch.show')"
        name="pageSize"
        class="w-20 shrink-0"
      >
        <USelect
          v-model="perPages"
          :items="optionsPerPage"
          class="w-full"
        />
      </UFormField>
    </div>
  </div>

  <div class="w-full z-0">
    <UTable
      ref="table"
      :data="data"
      :columns="columns"
      :loading="status === 'pending'"
    />

    <CustomPagination
      v-model="page"
      :total-pages="totalPages"
      :total-records="totalRecords"
      :per-pages="perPages"
      class="mt-4"
    />
  </div>

  <VehicleDialog
    v-model:is-dialog-visible="isDialogDetailVisible"
    :vehicle="editedItem"
  />

  <VehicleAddDialog
    v-model:is-dialog-visible="isDialogVisible"
    :vehicle="editedItem"
  />

  <DeleteDialog
    v-model:is-dialog-visible="isDeleteDialogVisible"
    :url="deletePath"
    @submit="onSubmit"
  />
</template>
