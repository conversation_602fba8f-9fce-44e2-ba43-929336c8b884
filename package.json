{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev --no-fork", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@fullcalendar/core": "^6.1.18", "@fullcalendar/daygrid": "^6.1.18", "@fullcalendar/interaction": "^6.1.18", "@fullcalendar/list": "^6.1.18", "@fullcalendar/multimonth": "^6.1.18", "@fullcalendar/scrollgrid": "^6.1.18", "@fullcalendar/timegrid": "^6.1.18", "@fullcalendar/vue3": "^6.1.18", "@nuxt/ui-pro": "^3.2.0", "@nuxtjs/i18n": "^9.5.6", "@pinia/nuxt": "0.11.1", "@vuepic/vue-datepicker": "^11.0.2", "axios": "^1.10.0", "date-fns": "^4.1.0", "nuxt": "^4.0.0", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "xlsx": "^0.18.5", "zod": "^3.25.67"}, "devDependencies": {"@nuxt/eslint": "1.5.2", "eslint": "^9.30.0", "typescript": "^5.8.3", "vue-tsc": "^3.0.2", "vue": "^3.5.17", "vue-router": "^4.5.1"}}