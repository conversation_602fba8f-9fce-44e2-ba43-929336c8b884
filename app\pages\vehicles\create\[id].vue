<script lang="ts" setup>
import VueDatePicker from "@vuepic/vue-datepicker";
import "@vuepic/vue-datepicker/dist/main.css";

import type { FormSubmitEvent } from "@nuxt/ui";
import { storeToRefs } from "pinia";
import z from "zod";
import { useOptionsStore } from "~/stores/options";
import type { ApiResponse } from "~/types";

definePageMeta({
  middleware: ["authenticated"],
});

const { $api } = useNuxtApp();
const { t } = useI18n();
const route = useRoute();
const router = useRouter();
const optionsStore = useOptionsStore();
const { optionsOrganization } = storeToRefs(optionsStore);
const toast = useToast();

const bookingSchema = z.object({
  organizationId: z.string(),
  project: z.string(),
  description: z.string(),
  vehicleId: z.string(),
  start: z.string(),
  end: z.string(),
  passengers: z.number().int().min(1),
  phone: z.string().optional(),
  driverName: z.string(),
  remark: z.string().optional(),
});

type Schema = z.output<typeof bookingSchema>;

const state = ref<Partial<Schema>>({
  organizationId: undefined,
  project: undefined,
  description: undefined,
  vehicleId: route?.params?.id as string,
  start: undefined, // Set to current date and time
  end: undefined, // Set to one hour later
  passengers: 1,
  phone: undefined,
  driverName: undefined,
  remark: undefined,
});

await callOnce(() => optionsStore.initialOptions());

async function onSubmit(event: FormSubmitEvent<Schema>) {
  const { data } = event;
  console.log("🚀 ~ onSubmit ~ data:", data);

  const response = await $api<ApiResponse<Schema>>("/api/reservation.php", {
    method: "POST",
    body: JSON.stringify(data),
  });
  if (response) {
    toast.add({
      title: response.code,
      description: response.message,
      icon: response.status ? "i-lucide-check" : "i-lucide-x",
      color: response.status ? "success" : "error",
    });
    if (!response.status) {
      return;
    }

    router.push("/bookings/0");
  }
}
</script>

<template>
  <UForm
    :schema="bookingSchema"
    :state="state"
    class="space-y-4"
    @submit="onSubmit"
  >
    <UPageCard
      :title="t('booking.title')"
      variant="naked"
      orientation="horizontal"
      class="mb-4 pt-4"
    >
      <UButton
        icon="i-lucide-save"
        :label="t('save')"
        color="success"
        class="w-fit lg:ms-auto"
        type="submit"
        loading-auto
      />
    </UPageCard>

    <UPageCard
      variant="subtle"
      class="mb-4"
    >
      <UFormField
        :label="t('booking.project')"
        class="basis-1/2"
        name="project"
      >
        <UInput
          v-model="state.project"
          class="w-full"
        />
      </UFormField>

      <UFormField
        :label="t('booking.description')"
        class=""
        name="description"
      >
        <UTextarea
          v-model="state.description"
          class="w-full"
        />
      </UFormField>

      <div class="flex flex-row gap-4">
        <UFormField
          :label="t('booking.organization')"
          class="basis-1/2"
          name="organizationId"
        >
          <USelect
            v-model="state.organizationId"
            :items="optionsOrganization"
            value-key="value"
            label-key="label"
            class="w-full"
          />
        </UFormField>

        <UFormField
          :label="t('booking.passengers')"
          class="basis-1/2"
          name="passengers"
        >
          <UInput
            v-model="state.passengers"
            type="number"
            class="w-full"
          />
        </UFormField>
      </div>

      <div class="flex flex-row gap-4">
        <UFormField
          :label="t('booking.driverName')"
          class="basis-1/2"
          name="driverName"
        >
          <UInput
            v-model="state.driverName"
            class="w-full"
          />
        </UFormField>

        <UFormField
          :label="t('booking.phone')"
          class="basis-1/2"
          name="phone"
        >
          <UInput
            v-model="state.phone"
            class="w-full"
          />
        </UFormField>
      </div>

      <div class="flex flex-row gap-4">
        <UFormField
          :label="t('booking.beginDate')"
          class="basis-1/2"
          name="start"
        >
          <VueDatePicker
            v-model="state.start"
            time-picker-inline
            model-type="yyyy-MM-dd hh:mm:ss"
          />
        </UFormField>

        <UFormField
          :label="t('booking.endDate')"
          class="basis-1/2"
          name="end"
        >
          <VueDatePicker
            v-model="state.end"
            time-picker-inline
            model-type="yyyy-MM-dd hh:mm:ss"
          />
        </UFormField>
      </div>

      <UFormField
        :label="t('booking.remark')"
        class=""
      >
        <UTextarea
          v-model="state.remark"
          class="w-full"
        />
      </UFormField>
    </UPageCard>
  </UForm>
</template>
