<script lang="ts" setup>
const { t } = useI18n();

export interface Stat {
  title: string;
  icon: string;
  value: number;
  color: string;
  bgColor: string;
  to?: string;
}

function formatCurrency(value: number): string {
  return value.toLocaleString("en-US", {
    style: "currency",
    currency: "USD",
    maximumFractionDigits: 0,
  });
}

function formatNumber(value: number): string {
  return value.toLocaleString("en-US", {
    maximumFractionDigits: 0,
  });
}

const baseStats = computed<Stat[]>(() => [
  {
    title: t("dashboard.stats.pending"),
    icon: "i-lucide-clock",
    value: 1000,
    color: "text-blue-500",
    bgColor: "bg-blue-200",
    to: "/bookings/0",
  },
  {
    title: t("dashboard.stats.approved"),
    icon: "i-lucide-check",
    value: 2000,
    color: "text-green-500",
    bgColor: "bg-green-200",
    to: "/bookings/1",
  },
  {
    title: t("dashboard.stats.rejected"),
    icon: "i-lucide-x",
    value: 30000,
    color: "text-yellow-500",
    bgColor: "bg-yellow-200",
    to: "/bookings/2",
  },
  {
    title: t("dashboard.stats.vehicle"),
    icon: "i-lucide-car",
    value: 30000,
    color: "text-red-500",
    bgColor: "bg-red-200",
    to: "/vehicles",
  },
]);

// const { data: stats } = await useAsyncData<Stat[]>('stats', async () => {
//   return baseStats.value.map((stat) => {
//    return {
//       title: stat.title,
//       icon: stat.icon,
//       value: formatNumber(stat.value),
//       color: stat.color,
//     }
//   })
// }, {
//   watch: [],
//   default: () => []
// })
</script>

<template>
  <UPageGrid class="grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
    <UPageCard
      v-for="(stat, index) in baseStats"
      :key="index"
      :to="stat.to"
      class=""
    >
      <div class="flex items-center bg-white rounded">
        <div
          :class="[
            'flex flex-shrink-0 items-center justify-center h-16 w-16 rounded',
            stat.bgColor,
          ]"
        >
          <UIcon :name="stat.icon" class="size-10" :class="stat.color" />
        </div>
        <div class="flex-grow flex flex-col ml-4 items-end justify-between">
          <span class="text-xl font-bold">{{ formatNumber(stat.value) }}</span>
          <span class="text-gray-500">{{ stat.title }}</span>
        </div>
      </div>
    </UPageCard>
  </UPageGrid>
</template>
