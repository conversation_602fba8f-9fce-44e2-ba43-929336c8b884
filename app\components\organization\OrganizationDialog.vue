<script lang="ts" setup>
import * as z from "zod";
import type { FormSubmitEvent } from "@nuxt/ui";
import type { ApiResponse } from "~/types";

const { $api } = useNuxtApp();
const toast = useToast();
const { t } = useI18n();
const optionsStore = useOptionsStore();
const {
  optionsProvince,
} = storeToRefs(optionsStore);

const organizationSchema = z.object({
  id: z.string().optional(),
  name: z.string(),
  description: z.string(),
  address: z.string(),
  locationId: z.string(),
});

type Schema = z.output<typeof organizationSchema>;

interface Props {
  organization?: Partial<Schema>;
  isDialogVisible?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  organization: () => ({
    id: undefined,
    name: undefined,
    description: undefined,
    address: undefined,
    locationId: undefined,
  }),
  isDialogVisible: false,
});
interface Emit {
  (e: "update:isDialogVisible", value: boolean): void;
  (e: "submit", value: Schema): void;
}
const emit = defineEmits<Emit>();

const state = ref<Partial<Schema>>(structuredClone(toRaw(props.organization)));

const isDialogVisible = ref(props.isDialogVisible);

watch(props, () => {
  state.value = structuredClone(toRaw(props.organization));
  isDialogVisible.value = props.isDialogVisible;
});

async function onSubmit(event: FormSubmitEvent<Schema>) {
  const { data } = event;

  const response = await $api<ApiResponse<Schema>>("/api/organization.php", {
    method: props.organization.id ? "PUT" : "POST",
    query: props.organization.id ? { id: props.organization.id } : undefined,
    body: JSON.stringify(data),
  });
  if (response) {
    toast.add({
      title: response.code,
      description: response.message,
      icon: response.status ? "i-lucide-check" : "i-lucide-x",
      color: response.status ? "success" : "error",
    });
    if (!response.status) {
      return;
    }
  }
  emit("update:isDialogVisible", false);
  emit("submit", data);
}

const resetForm = () => {
  emit("update:isDialogVisible", false);
  state.value = structuredClone(toRaw(props.organization));
};
</script>

<template>
  <UModal
    v-model:open="isDialogVisible"
    :title="props.organization?.name ? t('edit') : t('add')"
    @update:open="(value) => emit('update:isDialogVisible', value)"
  >
    <template #body>
      <UForm
        :schema="organizationSchema"
        :state="state"
        class="space-y-4"
        @submit="onSubmit"
      >
        <UFormField
          :label="t('table.agencyName')"
          name="name"
        >
          <UInput
            v-model="state.name"
            class="w-full"
          />
        </UFormField>

        <UFormField
          :label="t('vehicle.description')"
          name="description"
        >
          <UTextarea
            v-model="state.description"
            class="w-full"
          />
        </UFormField>

        <UFormField
          :label="t('table.address')"
          name="address"
        >
          <UTextarea
            v-model="state.address"
            class="w-full"
          />
        </UFormField>

        <UFormField
          :label="t('location')"
          name="locationId"
        >
          <USelectMenu
            v-model="state.locationId"
            class="w-full"
            :items="optionsProvince"
            value-key="value"
            label-key="label"
          />
        </UFormField>
        <div class="flex justify-end gap-2">
          <UButton
            :label="t('cancel')"
            color="neutral"
            variant="subtle"
            @click="resetForm"
          />
          <UButton
            :label="t('save')"
            color="primary"
            variant="solid"
            type="submit"
          />
        </div>
      </UForm>
    </template>
  </UModal>
</template>
