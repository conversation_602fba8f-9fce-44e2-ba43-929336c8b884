<script lang="ts" setup>
import dayGridPlugin from "@fullcalendar/daygrid";
import interactionPlugin from "@fullcalendar/interaction";
import FullCalendar from "@fullcalendar/vue3";
import type { CalendarOptions, EventClickArg } from "@fullcalendar/core";
import type { Booking } from "~/types";
import { useBreakpoints } from "@vueuse/core";

type Event = {
  id: string;
  projectName: string;
  start: string;
  end: string;
  licensePlate: string;
  vehicleModel: string;
};

type CalendarEvent = Event & {
  title: string;
  backgroundColor: string;
  textColor: string;
  borderColor: string;
  class: string;
};

const breakpoints = useBreakpoints({
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
});

const isMobile = breakpoints.smaller("md"); // < 768px

const { locale } = useI18n();
// const toast = useToast();
const profileStore = useProfileStore();
const { profile } = storeToRefs(profileStore);

const isDialogVisible = ref(false);
const currentMonth = ref(new Date().getMonth() + 1); // +1 because JS months are 0-based
const currentYear = ref(new Date().getFullYear());
const booking = ref<Booking | undefined>(undefined);

const url = computed(() => {
  return `/api/calendar.php?organizationId=${profile.value?.organizationId}&month=${currentYear.value}-${currentMonth.value?.toString().padStart(2, "0")}-01`;
});

const { data, refresh } = await useApi<CalendarEvent[]>(url, {
  transform: (response) => {
    if (!response.details?.length) return [];
    return response.details.map((event: Event, index: number) => ({
      id: event.id,
      title: event.projectName,
      start: event.start,
      end: event.end,
      licensePlate: event.licensePlate,
      vehicleModel: event.vehicleModel,
      backgroundColor: listColors.value[index % listColors.value.length],
      textColor: "#000000",
      borderColor: listColors.value[index % listColors.value.length],
    })) as CalendarEvent[];
  },
});

watch([currentMonth, currentYear], () => {
  refresh();
});

const listColors = ref<string[]>([
  "#FF0000", "#FF7F00", "#FFFF00", "#7FFF00", "#00FF00",
  "#00FF7F", "#00FFFF", "#007FFF", "#0000FF", "#7F00FF",
  "#FF00FF", "#FF007F", "#FF1493", "#FF4500", "#FFD700",
  "#ADFF2F", "#32CD32", "#40E0D0", "#1E90FF", "#4169E1",
  "#8A2BE2", "#C71585", "#DC143C", "#FF6347", "#FFA500",
  "#9ACD32", "#00CED1", "#4682B4", "#6A5ACD", "#9932CC",
  "#FF69B4", "#FFB6C1", "#87CEFA", "#20B2AA", "#B22222",
  "#228B22",
]);

const calendarOptions = computed<CalendarOptions>(() => ({
  plugins: [dayGridPlugin, interactionPlugin],

  locale: locale.value,
  initialView: "dayGridMonth",

  stickyHeaderDates: false,
  editable: false,
  headerToolbar: {
    left: isMobile.value ? "" : "prevYear,prev,today,next,nextYear",
    center: isMobile.value ? "prevYear,prev,today,next,nextYear" : "title",
    right: isMobile.value ? "" : "dayGridDay,dayGridWeek,dayGridMonth,dayGridYear",
  },

  eventClick: handleEventClick,
  datesSet: (info) => {
    // info.start is the first visible date, info.view.currentStart works too
    const date = info.view.currentStart;
    currentMonth.value = date.getMonth() + 1; // +1 because JS months are 0-based
    currentYear.value = date.getFullYear();
  },

  events: data.value,
}));

const handleEventClick = async (arg: EventClickArg) => {
  console.log("🚀 ~ handleEventClick ~ arg:", arg.event.extendedProps);
  const { data, error } = await useApi<Booking>(`/api/reservation.php?id=${arg.event.id}`, {
    transform: (response) => {
      return response.details as Booking;
    },
  });

  if (error.value) {
    console.error("🚀 ~ handleEventClick ~ error:", error.value);
    return;
  }

  booking.value = data.value;
  isDialogVisible.value = true;
};
</script>

<template>
  <FullCalendar :options="calendarOptions" />

  <BookingDialog
    v-model:is-dialog-visible="isDialogVisible"
    :booking="booking"
  />
</template>

<style>
/* Because the doc's navbar is blocking it 🙄 */
.fc .fc-scrollgrid-section-header.fc-scrollgrid-section-sticky > * {
  top: 55px;
}
.fc-event {
  cursor: pointer;
}
.fc-event-draggable {
  cursor: grab;
}
.fc-event-dragging {
  cursor: grabbing;
}
</style>
