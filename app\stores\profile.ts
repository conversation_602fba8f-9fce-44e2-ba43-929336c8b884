import type { Profile, Token } from "~/types";

export const useProfileStore = defineStore("profileStore", () => {
  const profile = ref<Profile | null>(null);
  const token = ref<Token | null>(null);

  const setProfile = (data: Profile) => {
    profile.value = data;
  };

  const setToken = (data: Token) => {
    token.value = data;
  };

  return {
    profile,
    setProfile,
    token,
    setToken,
  };
},
{
  persist: {
    storage: localStorage,
  },
});
