<script lang="ts" setup>
import { storeToRefs } from "pinia";
import type { TableColumn } from "@nuxt/ui";
import { h, resolveComponent } from "vue";
import type { Organization } from "~/types";
import { useOptionsStore } from "~/stores/options";

definePageMeta({
  middleware: ["authenticated"],
});

const toast = useToast();
const { t } = useI18n();
const optionsStore = useOptionsStore();
const { optionsPerPage } = storeToRefs(optionsStore);
const UButton = resolveComponent("UButton");

const isDialogVisible = ref(false);
const isDeleteDialogVisible = ref(false);
const editedItem = ref<Organization | undefined>(undefined);
const deletePath = ref<string | undefined>(undefined);

const search = ref("");
const page = ref(1);
const perPages = ref(10);
const totalRecords = ref(0);
const totalPages = ref(0);

const columns = computed<TableColumn<Organization>[]>(() => [
  {
    accessorKey: "name",
    header: () => h("span", t("table.agencyName")),
  },
  {
    accessorKey: "address",
    header: () => h("span", t("table.address")),
    cell: ({ row }) => {
      return h("div", { class: "" }, [
        h("p", { class: "font-medium " }, row.original.address),
        h("div", { class: "flex gap-2" }, [
          h("span", row.original.amphurTH),
          h("span", row.original.districtTH),
          h("span", row.original.provinceTH),
        ]),
      ]);
    },
  },
  // {
  //   accessorKey: "status",
  //   header: () => h("span", t("table.status")),
  // },
  {
    accessorKey: "createDate",
    header: () => h("span", t("table.createAt")),
  },
  {
    accessorKey: "createBy",
    header: () => h("span", t("table.createBy")),
  },
  {
    id: "actions",
    cell: ({ row }) => {
      return h("div", { class: "text-right flex gap-2" }, [
        h(UButton, {
          icon: "i-lucide-edit",
          label: t("edit"),
          color: "success",
          class: "ml-auto",
          onClick: () => {
            const organization = row.original as Organization;
            openEditOrganizationDialog(organization);
          },
        }),
        h(UButton, {
          icon: "i-lucide-trash",
          label: t("delete"),
          color: "error",
          onClick: () => {
            // Handle delete action
            deletePath.value = `/api/organization.php?id=${row.original.id}`;
            isDeleteDialogVisible.value = true;
          },
        }),
      ]);
    },
  },
]);

const openEditOrganizationDialog = (organization: Organization) => {
  editedItem.value = organization;

  isDialogVisible.value = true;
};

const { data, status, refresh } = await useApi<Organization[]>("/api/organization.php",
  {
    query: {
      search: search,
      page: page,
      perPages: perPages,
    },
    transform: (response): Organization[] => {
      if (!response?.status) {
        toast.add({
          title: response.code,
          description: response.message,
          icon: "i-lucide-x",
          progress: false,
          color: "warning",
        });
        return [];
      }
      page.value = response.pagination.page;
      perPages.value = response.pagination.perPages;
      totalRecords.value = response.pagination.totalRecords;
      totalPages.value = response.pagination.totalPages;
      return response.details?.map(item => item) as Organization[];
    },
  },
);

const onSubmit = () => {
  editedItem.value = undefined;
  deletePath.value = undefined;
  isDialogVisible.value = false;
  isDeleteDialogVisible.value = false;
  refresh();
};
</script>

<template>
  <div class="flex items-center gap-2 mb-4">
    <UIcon
      name="i-codicon-organization"
      class="size-5"
    />
    <h1 class="text-xl">
      {{ t("navSetting.organization") }}
    </h1>

    <UButton
      icon="i-lucide-plus"
      form="settings"
      :label="t('add')"
      color="info"
      type="submit"
      class="w-fit lg:ms-auto"
      @click="editedItem = undefined, isDialogVisible = true"
    />
  </div>

  <div class="flex flex-col gap-3 md:flex-row md:items-end md:justify-between md:text-end">
    <div class="flex w-full flex-row gap-1.5 sm:flex-row sm:items-end">
      <UFormField
        :label="t('formSearch.search')"
        name="search"
        class="w-full sm:w-72"
      >
        <UInput
          v-model="search"
          class="w-full"
        />
      </UFormField>

      <UButton class="self-end sm:self-auto">
        {{ t("formSearch.search") }}
      </UButton>
    </div>
    <div class="flex w-full flex-wrap gap-1.5 justify-end md:w-auto lg:flex-nowrap">
      <UFormField
        :label="t('formSearch.show')"
        name="pageSize"
        class="w-20 shrink-0"
      >
        <USelect
          v-model="perPages"
          :items="optionsPerPage"
          class="w-full"
        />
      </UFormField>
    </div>
  </div>

  <div class="w-full z-0">
    <UTable
      ref="table"
      :data="data"
      :columns="columns"
      :loading="status === 'pending'"
    />

    <CustomPagination
      v-model="page"
      :total-pages="totalPages"
      :total-records="totalRecords"
      :per-pages="perPages"
      class="mt-4"
    />
  </div>

  <OrganizationDialog
    v-model:is-dialog-visible="isDialogVisible"
    :organization="editedItem"
    @submit="onSubmit"
  />

  <DeleteDialog
    v-model:is-dialog-visible="isDeleteDialogVisible"
    :url="deletePath"
    @submit="onSubmit"
  />
</template>
