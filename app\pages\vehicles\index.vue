<script setup lang="ts">
import { storeToRefs } from "pinia";
import type { TableColumn } from "@nuxt/ui";
import { h, resolveComponent } from "vue";
import { useOptionsStore } from "~/stores/options";
import type { Vehicle } from "~/types";

definePageMeta({
  middleware: ["authenticated"],
});

const { t } = useI18n();
const router = useRouter();
const optionsStore = useOptionsStore();
const { optionsType, optionsBrand, optionsPerPage } = storeToRefs(optionsStore);
const toast = useToast();

const UButton = resolveComponent("UButton");
const UBadge = resolveComponent("UBadge");

const isDialogVisible = ref(false);
const search = ref("");
const page = ref(1);
const perPages = ref(10);
const totalRecords = ref(0);
const totalPages = ref(0);
const selectTypeVehicle = ref("it");
const selectBrandVehicle = ref("active");
const editedItem = ref<Vehicle | undefined>(undefined);

const columns = computed<TableColumn<Vehicle>[]>(() => [
  {
    accessorKey: "licensePlate",
    header: () => h("span", t("table.plateNo")),
    cell: ({ row }) => {
      return h("div", { class: "flex items-center gap-3" }, [

        h("div", undefined, [
          h("p", { class: "font-medium text-highlighted" }, row.original.licensePlate),
          h("p", { class: "" }, `${row.original.provinceName}`),
        ]),
      ]);
    },
  },
  {
    accessorKey: "model.brand",
    header: () => h("span", t("table.brand")),
    cell: ({ row }) => {
      return h("div", undefined, [
        h("p", { class: "" }, `${row.original?.model?.brand}`),
        h(UBadge, { color: row.original.color === "WHITE" ? "neutral" : row.original.color, variant: "subtle" }, [
          h("span", undefined, row.original.color),
        ]),

      ]);
    },
  },
  {
    accessorKey: "model.type",
    header: () => h("span", t("table.type")),
  },
  {
    accessorKey: "model.engine",
    header: () => h("span", t("table.engine")),
  },
  {
    accessorKey: "model.seat",
    header: () => h("span", t("table.numberOfSeats")),
  },
  {
    accessorKey: "model.year",
    header: () => h("span", t("table.year")),
  },
  {
    accessorKey: "organizationName",
    header: () => h("span", t("table.organization")),
  },
  {
    id: "actions",
    cell: ({ row }) => {
      return h("div", { class: "flex justify-end gap-2" }, [
        h(UButton, {
          icon: "tdesign:cart-add",
          label: t("pageVehicle.booking"),
          color: "info",
          class: "ml-auto",
          onClick: () => {
            router.push({
              name: "vehicles-create-id",
              params: { id: row.original.id },
            });
          },
        }),
        h(UButton, {
          icon: "ant-design:info-circle-outlined",
          label: t("pageVehicle.detail"),
          color: "warning",
          onClick: () => {
            editedItem.value = row.original;
            isDialogVisible.value = true;
          },
        }),
      ]);
    },
  },
]);

const { data, status } = await useApi<Vehicle[]>(`/api/vehicle.php`, {
  query: {
    search: search,
    page: page,
    perPages: perPages,
  },
  transform: (response) => {
    if (!response?.status) {
      toast.add({
        title: response.code,
        description: response.message,
        icon: "i-lucide-x",
        progress: false,
        color: "warning",
      });
      return [];
    }

    page.value = response?.pagination?.page;
    perPages.value = response?.pagination?.perPages;
    totalRecords.value = response?.pagination?.totalRecords;
    totalPages.value = response?.pagination?.totalPages;
    return response.details?.map(item => item) || [];
  },

});
</script>

<template>
  <div class="flex items-center gap-2 py-2">
    <UIcon
      name="i-lucide-car"
      class="size-5"
    />
    <h1 class="text-xl">
      {{ t("pageVehicle.title") }}
    </h1>
  </div>

  <div class="flex flex-col gap-3 md:flex-row md:items-end md:justify-between md:text-end">
    <!-- Left: search + button -->
    <div class="flex w-full flex-row gap-1.5 sm:flex-row sm:items-end">
      <UFormField
        :label="t('formSearch.search')"
        name="search"
        class="w-3/4 sm:w-72"
      >
        <UInput
          v-model="search"
          class="w-full"
        />
      </UFormField>

      <UButton class="self-end sm:self-auto">
        {{ t('formSearch.search') }}
      </UButton>
    </div>

    <!-- Right: filters -->
    <div class="flex w-full flex-wrap items-end gap-1.5 md:w-1/2">
      <UFormField
        :label="t('formSearch.type')"
        name="vehicle"
        class="w-full sm:w-44 shrink-0"
      >
        <USelect
          v-model="selectTypeVehicle"
          :items="optionsType"
          value-key="value"
          class="w-full"
        />
      </UFormField>

      <UFormField
        :label="t('formSearch.brand')"
        name="brandVehicle"
        class="w-full sm:w-48 shrink-0"
      >
        <USelect
          v-model="selectBrandVehicle"
          :items="optionsBrand"
          value-key="value"
          class="w-full"
        />
      </UFormField>

      <UFormField
        :label="t('formSearch.show')"
        name="perPages"
        class="w-24 shrink-0"
      >
        <USelect
          v-model="perPages"
          :items="optionsPerPage"
          class="w-full"
        />
      </UFormField>
    </div>
  </div>

  <div class="w-full z-0">
    <UTable
      ref="table"
      :data="data"
      :columns="columns"
      :loading="status === 'pending'"
    />

    <CustomPagination
      v-model="page"
      :total-pages="totalPages"
      :total-records="totalRecords"
      :per-pages="perPages"
      class="mt-4"
    />
  </div>

  <VehicleDialog
    v-model:is-dialog-visible="isDialogVisible"
    :vehicle="editedItem"
  />
</template>
