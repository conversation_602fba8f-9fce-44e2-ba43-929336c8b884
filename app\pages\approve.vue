<script setup lang="ts">
import { storeToRefs } from "pinia";
import type { TableColumn } from "@nuxt/ui";
import { sub } from "date-fns";
import { h, resolveComponent } from "vue";
import type { Range, Booking, ApiResponse } from "~/types";
import { useOptionsStore } from "~/stores/options";

definePageMeta({
  middleware: ["authenticated"],
});

const { $api } = useNuxtApp();
const toast = useToast();
const store = useProfileStore();
const { profile } = storeToRefs(store);
const { t } = useI18n();
const optionsStore = useOptionsStore();
const {
  optionsType,
  optionsBrand,
  optionsVehicle,
  optionsStatus,
  optionsDriver,
  optionsPerPage,
} = storeToRefs(optionsStore);

const UBadge = resolveComponent("UBadge");
const UButton = resolveComponent("UButton");

const isDialogVisible = ref(false);
const isDialogConfirmVisible = ref(false);
const isDialogConfirmApprovalVisible = ref(false);

const search = ref("");
const page = ref(1);
const perPages = ref(10);
const totalRecords = ref(0);
const totalPages = ref(0);

const selectVehicle = ref("all");
const selectStatus = ref("all");
const selectTypeVehicle = ref("it");
const selectBrandVehicle = ref("active");
const selectChauffeur = ref("all");
const rejectionReasons = ref<string>("");

const editedItem = ref<Booking | undefined>(undefined);
const selectedRow = ref<Booking | undefined>(undefined);

const range = shallowRef<Range>({
  start: sub(new Date(), { days: 14 }),
  end: new Date(),
});

const columns = computed<TableColumn<Booking>[]>(() => [
  {
    accessorKey: "projectName",
    header: () => h("span", t("table.usageDetails")),
    cell: ({ row }) => {
      return h("div", undefined, [
        h("p", { class: "font-medium text-highlighted" }, row.original.projectName),
        h("p", { class: "" }, `${row.original.description}`),
      ]);
    },
  },
  {
    accessorKey: "vehicleModel",
    header: () => h("span", t("table.vehicle")),
  },
  {
    accessorKey: "passengers",
    header: () => h("span", t("table.passengers")),
  },
  {
    accessorKey: "driverName",
    header: () => h("span", t("table.driverName")),
    cell: ({ row }) => {
      return h("div", undefined, [
        h("p", { class: "font-medium text-highlighted" }, `${row.original.driverName} ${row.original.phone ? `(${row.original.phone})` : ""}`),
        h("p", { class: "" }, `${row.original.organizationName}`),
      ]);
    },
  },
  {
    accessorKey: "start",
    header: () => h("span", t("table.dateReservation")),
    cell: ({ row }) => {
      return h("div", undefined, [
        h("p", { class: "font-medium text-highlighted" }, row.original.start),
        h("p", { class: "" }, `${row.original.end}`),
      ]);
    },
  },
  {
    accessorKey: "status",
    header: () => h("span", t("table.status")),
    cell: ({ row }) => {
      const status = row.getValue("status");

      const color = {
        "Approved": "success" as const,
        "Pending approval": "info" as const,
        "Rejected": "error" as const,
        "Confirmed": "neutral" as const,
      }[row.getValue("status") as string];

      return h(UBadge, { variant: "subtle", color }, () => status);
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      return h("div", { class: "text-right flex gap-2" }, [
        h(UButton, {
          icon: "ant-design:info-circle-outlined",
          label: t("pageVehicle.detail"),
          color: "neutral",
          onClick: () => {
            isDialogVisible.value = true;
            editedItem.value = row.original;
          },
        }),
        h(UButton, {
          icon: "i-lucide-user-x",
          label: t("cancelByCustomer"),
          color: "info",
          class: "ml-auto",
          onClick: () => {
            selectedRow.value = row.original;
            isDialogConfirmVisible.value = true;
          },
        }),
        h(UButton, {
          icon: "i-lucide-x",
          label: t("cancelByStaff"),
          color: "warning",
          onClick: () => {
            selectedRow.value = row.original;
            isDialogConfirmVisible.value = true;
          },
        }),
        row.original.status === "Approved"
          ? h(UButton, {
              icon: "i-lucide-check",
              color: "success",
              onClick: () => {
                selectedRow.value = row.original;
                isDialogConfirmApprovalVisible.value = true;
              },
            })
          : null,
      ]);
    },
  },
]);

await callOnce(() => optionsStore.initialOptions());

const { data, status } = await useApi<Booking[]>(`/api/reservation.php`, {
  query: {
    organizationId: profile.value?.organizationId,
    search: search,
    page: page,
    perPages: perPages,
  },

  transform: (response) => {
    if (!response?.status) {
      toast.add({
        title: response.code,
        description: response.message,
        icon: "i-lucide-x",
        progress: false,
        color: "warning",
      });
      return [];
    }
    page.value = response?.pagination?.page;
    perPages.value = response?.pagination?.perPages;
    totalRecords.value = response?.pagination?.totalRecords;
    totalPages.value = response?.pagination?.totalPages;
    return response?.details?.map(item => item) as Booking[];
  },
});

const rejectReservation = async (): Promise<boolean> => {
  const response = await $api<ApiResponse<Booking>>("/api/rejection.php", {
    method: "PUT",
    query: { id: selectedRow.value?.id },
    body: JSON.stringify({ rejectionReasons: rejectionReasons.value }),
  });

  if (response) {
    toast.add({
      title: response.code,
      description: response.message,
      icon: response.status ? "i-lucide-check" : "i-lucide-x",
      color: response.status ? "success" : "error",
    });
    if (!response.status) {
      return false;
    }
  }
  selectedRow.value = undefined;
  isDialogConfirmVisible.value = false;
  rejectionReasons.value = "";
  return true;
};

const approve = async (): Promise<boolean> => {
  const response = await $api<ApiResponse<Booking>>("/api/approval.php", {
    method: "PUT",
    query: { id: selectedRow.value?.id },
  });

  if (response) {
    toast.add({
      title: response.code,
      description: response.message,
      icon: response.status ? "i-lucide-check" : "i-lucide-x",
      color: response.status ? "success" : "error",
    });
    if (!response.status) {
      return false;
    }
  }
  isDialogConfirmVisible.value = false;
  return true;
};
</script>

<template>
  <div class="flex items-center gap-2 py-2">
    <UIcon
      name="i-lucide-check-circle"
      class="size-5"
    />
    <h1 class="text-xl">
      {{ t("nav.approve") }}
    </h1>
  </div>

  <div class="flex flex-col gap-3 md:flex-row md:items-end md:justify-between md:text-end">
    <div class="flex w-full flex-col gap-1.5 sm:flex-row sm:items-end">
      <UFormField
        :label="t('formSearch.search')"
        name="search"
        class="w-full sm:w-72"
      >
        <UInput
          v-model="search"
          class="w-full"
        />
      </UFormField>

      <UButton class="self-end sm:self-auto">
        {{ t("formSearch.search") }}
      </UButton>
    </div>
    <div class="flex w-full flex-wrap gap-1.5 justify-end md:w-auto lg:flex-nowrap">
      <UFormField
        :label="t('formSearch.startDate')"
        name="dateRange"
        class="w-full sm:w-60 shrink-0"
      >
        <DateRangePicker
          v-model="range"
          class="w-full"
        />
      </UFormField>

      <UFormField
        :label="t('formSearch.vehicle')"
        name="vehicle"
        class="w-full sm:w-40 shrink-0"
      >
        <USelect
          v-model="selectVehicle"
          :items="optionsVehicle"
          value-key="value"
          class="w-full"
        />
      </UFormField>

      <UFormField
        :label="t('formSearch.type')"
        name="vehicle"
        class="w-full sm:w-40 shrink-0"
      >
        <USelect
          v-model="selectTypeVehicle"
          :items="optionsType"
          value-key="value"
          class="w-full"
        />
      </UFormField>

      <UFormField
        :label="t('formSearch.brand')"
        name="brandVehicle"
        class="w-full sm:w-40 shrink-0"
      >
        <USelect
          v-model="selectBrandVehicle"
          :items="optionsBrand"
          value-key="value"
          class="w-full"
        />
      </UFormField>

      <UFormField
        :label="t('formSearch.chauffeur')"
        name="chauffeur"
        class="w-full sm:w-40 shrink-0"
      >
        <USelect
          v-model="selectChauffeur"
          :items="optionsDriver"
          value-key="value"
          class="w-full"
        />
      </UFormField>

      <UFormField
        :label="t('formSearch.status')"
        name="status"
        class="w-full sm:w-40 shrink-0"
      >
        <USelect
          v-model="selectStatus"
          :items="optionsStatus"
          value-key="value"
          class="w-full"
        />
      </UFormField>

      <UFormField
        :label="t('formSearch.show')"
        name="perPages"
        class="w-20 shrink-0"
      >
        <USelect
          v-model="perPages"
          :items="optionsPerPage"
          class="w-full"
        />
      </UFormField>
    </div>
  </div>

  <div class="w-full z-0">
    <UTable
      ref="table"
      :loading="status === 'pending'"
      :data="data"
      :columns="columns"
    />

    <CustomPagination
      v-model="page"
      :total-pages="totalPages"
      :total-records="totalRecords"
      :per-pages="perPages"
      class="mt-4"
    />
  </div>

  <BookingDialog
    v-model:is-dialog-visible="isDialogVisible"
    :booking="editedItem"
  />

  <ComfirmDialog
    v-model:is-dialog-visible="isDialogConfirmVisible"
    :call-back="rejectReservation"
  >
    <template #body>
      <UFormField
        :label="t('booking.reason')"
        name="reason"
      >
        <UTextarea
          v-model="rejectionReasons"
          class="w-full"
        />
      </UFormField>
    </template>
  </ComfirmDialog>

  <ComfirmDialog
    v-model:is-dialog-visible="isDialogConfirmApprovalVisible"
    :call-back="approve"
  />
</template>
