<script lang="ts" setup>
import * as z from "zod";
import type { FormSubmitEvent } from "@nuxt/ui";
import type { Accessories } from "~/types";

const { t } = useI18n();

const accessoriesSchema = z.object({
  name: z.string(),
  status: z.boolean(),
});

interface Props {
  accessories?: Accessories;
  isDialogVisible: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  accessories: () => ({
    id: 0,
    name: null,
    status: true,
    createAt: new Date().toISOString(),
    createBy: "system",
  }),
});
interface Emit {
  (e: "update:isDialogVisible", value: boolean): void;
  (e: "submit", value: Accessories): void;
}
const emit = defineEmits<Emit>();

let state = reactive<Accessories>(structuredClone(toRaw(props.accessories)));

watch(props, () => {
  state = structuredClone(toRaw(props.accessories));
});

async function onSubmit(event: FormSubmitEvent<Accessories>) {
  const { data } = event;
  emit("update:isDialogVisible", false);
  emit("submit", data);
}

const resetForm = () => {
  emit("update:isDialogVisible", false);
  state = structuredClone(toRaw(props.accessories));
};
</script>

<template>
  <UModal
    v-model:open="props.isDialogVisible"
    :title="props.accessories?.name ? t('edit') : t('add')"
    @update:open="(value: boolean) => emit('update:isDialogVisible', value)"
  >
    <template #body>
      <UForm
        :schema="accessoriesSchema"
        :state="state"
        class="space-y-4"
        @submit="onSubmit"
      >
        <UFormField
          :label="t('table.name')"
          name="name"
        >
          <UInput
            v-model="state.name"
            class="w-full"
          />
        </UFormField>

        <UFormField
          :label="t('table.status')"
          name="status"
        >
          <UCheckbox
            v-model="state.status"
            :label="
              state.status
                ? t('enableOptions.enable')
                : t('enableOptions.disable')
            "
          />
        </UFormField>

        <div class="flex justify-end gap-2">
          <UButton
            :label="t('cancel')"
            color="neutral"
            variant="subtle"
            @click="resetForm"
          />
          <UButton
            :label="t('save')"
            color="primary"
            variant="solid"
            type="submit"
          />
        </div>
      </UForm>
    </template>
  </UModal>
</template>
