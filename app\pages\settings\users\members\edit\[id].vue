<script lang="ts" setup>
import { storeToRefs } from "pinia";
import { useOptionsStore } from "~/stores/options";
import type { ApiResponse } from "~/types";

import type { FormSubmitEvent } from "@nuxt/ui";
import * as z from "zod";

definePageMeta({
  middleware: ["authenticated"],
});

const { $api } = useNuxtApp();
const route = useRoute();
const { t } = useI18n();
const toast = useToast();
const optionsStore = useOptionsStore();
const { optionsPermission, optionsOrganization } = storeToRefs(optionsStore);

// type PartialExcept<T, K extends keyof T> = Partial<T> & Pick<T, K>;
const schema = z.object({
  id: z.string().optional(),
  email: z.string(),
  fullname: z.string(),
  jobTitle: z.string(),
  notificationCount: z.number().min(0).optional(),
  organizationId: z.string().optional(),
  organzationName: z.string().optional(),
  permissionId: z.string().optional(),
  permissionName: z.string().optional(),
  phone: z.string().optional(),
  signatureLevel: z.number().min(0).optional(),
  username: z.string().min(2).max(100).optional(),
});

type User = z.output<typeof schema>;

const state = ref<Partial<User>>({
  email: "",
  fullname: "",
  jobTitle: "",
  notificationCount: 0,
  organizationId: "",
  permissionId: "",
  permissionName: "",
  phone: "",
  signatureLevel: 0,
  username: "",
});

const { data } = await useApi<User>("/api/useraccount.php?id=" + route.params.id, {
  transform: (response) => {
    // Transform the response if needed
    return response.details as User;
  },
});

watch(data, (newData) => {
  if (newData) {
    state.value = {
      ...state.value,
      ...newData,
    };
  }
}, { immediate: true });

async function onSubmit(event: FormSubmitEvent<User>) {
  const { data } = event;
  console.log("🚀 ~ onSubmit ~ data:", data);

  const response = await $api<ApiResponse<User>>("/api/useraccount.php", {
    method: "PUT",
    query: { id: state.value.id },
    body: JSON.stringify(data),
  });
  if (response) {
    toast.add({
      title: response.code,
      description: response.message,
      icon: response.status ? "i-lucide-check" : "i-lucide-x",
      color: response.status ? "success" : "error",
    });
    if (!response.status) {
      return;
    }

    navigateTo(`/settings/users/members`);
  }
}
</script>

<template>
  <UForm
    :schema="schema"
    :state="state"
    class="space-y-4"
    @submit="onSubmit"
  >
    <UPageCard
      :title="t('formUser.main')"
      variant="naked"
      orientation="horizontal"
      class="mb-4 "
    >
      <UButton
        :label="t('save')"
        color="success"
        type="submit"
        class="w-fit lg:ms-auto"
      />
    </UPageCard>

    <!--  Details of User -->
    <UPageCard
      variant="subtle"
      :title="t('formUser.secondTitle')"
      class="mb-4"
    >
      <div class="flex flex-row gap-4">
        <UFormField
          :label="t('formUser.nameSurname')"
          class="basis-1/2"
          name="fullname"
        >
          <UInput
            v-model="state.fullname"
            class="w-full"
          />
        </UFormField>

        <UFormField
          :label="t('formUser.email')"
          class="basis-1/2"
          name="email"
        >
          <UInput
            v-model="state.email"
            class="w-full"
          />
        </UFormField>
      </div>

      <div class="flex flex-row gap-4">
        <UFormField
          :label="t('formUser.username')"
          class="basis-1/2"
          name="username"
        >
          <UInput
            v-model="state.username"
            class="w-full"
          />
        </UFormField>

        <UFormField
          :label="t('formUser.phone')"
          class="basis-1/2"
          name="phone"
        >
          <UInput
            v-model="state.phone"
            class="w-full"
          />
        </UFormField>
      </div>

      <div class="flex flex-col gap-4 md:flex-row">
        <UFormField
          :label="t('formUser.jobTitle')"
          class="w-full md:basis-1/2"
          name="jobTitle"
        >
          <UInput
            v-model="state.jobTitle"
            class="w-full"
          />
        </UFormField>

        <UFormField
          :label="t('formUser.notificationCount')"
          class="w-full md:basis-1/2"
          name="notificationCount"
        >
          <UInput
            v-model.number="state.notificationCount"
            class="w-full"
          />
        </UFormField>

        <UFormField
          :label="t('organization')"
          class="w-full md:basis-1/2"
          name="organizationId"
        >
          <USelect
            v-model="state.organizationId"
            :items="optionsOrganization"
            class="w-full"
            item-value="id"
            item-label="name"
          />
        </UFormField>
      </div>
    </UPageCard>

    <!-- Other Details -->
    <UPageCard
      variant="subtle"
      :title="t('formUser.permission')"
      class="mb-4"
    >
      <div class="flex flex-col gap-4 md:flex-row">
        <UFormField
          :label="t('formUser.permission')"
          class="w-full md:basis-1/3"
          name="permissionId"
        >
          <USelect
            v-model="state.permissionId"
            :items="optionsPermission"
            class="w-full"
            item-value="id"
            item-label="name"
          />
        </UFormField>

        <UFormField
          :label="t('formUser.approvel') + ' (Approval Level)'"
          class="w-full md:basis-1/3"
          name="signatureLevel"
        >
          <USelect
            v-model="state.signatureLevel"
            :items="[0, 1, 2, 3, 4, 5]"
            class="w-full"
          />
        </UFormField>
      </div>
    </UPageCard>

    <UPageCard
      variant="subtle"
      :title="t('formUser.singnatory')"
      class="mb-4"
    >
      <div class="flex flex-row gap-4">
        <UFormField
          :label="t('formUser.singnatory') "
          class="w-full md:basis-2/3"
          name="signatory"
        >
          <UInput
            class="w-full"
          />
        </UFormField>
      </div>
    </UPageCard>
  </UForm>
</template>
