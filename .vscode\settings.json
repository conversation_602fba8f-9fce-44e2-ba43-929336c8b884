{
    "files.eol": "\n",
    "editor.tabSize": 2,
    "files.insertFinalNewline": true,
    "files.trimFinalNewlines": true,
    "files.trimTrailingWhitespace": true,
    "typescript.format.semicolons": "insert",
    "typescript.preferences.quoteStyle": "double",
    "eslint.useFlatConfig": true,
    "eslint.format.enable": true,
    "editor.formatOnSave": true,
    "eslint.validate": [
        "javascript",
        "typescript",
        "vue",
    ],
    "files.associations": {
        "*.css": "tailwindcss"
    },
    "editor.quickSuggestions": {
        "strings": "on"
    },
    "tailwindCSS.classAttributes": [
        "class",
        "ui"
    ],
    "tailwindCSS.experimental.classRegex": [
        [
            "ui:\\s*{([^)]*)\\s*}",
            "(?:'|\"|`)([^']*)(?:'|\"|`)"
        ]
    ]
}
