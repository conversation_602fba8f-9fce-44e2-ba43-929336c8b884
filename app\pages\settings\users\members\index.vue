<script setup lang="ts">
import { storeToRefs } from "pinia";
import type { TableColumn } from "@nuxt/ui";
import { h, resolveComponent } from "vue";
import type { User } from "~/types";
import { useOptionsStore } from "~/stores/options";

definePageMeta({
  middleware: ["authenticated"],
});

const profileStore = useProfileStore();
const { profile } = storeToRefs(profileStore);
const toast = useToast();
const { t } = useI18n();
const optionsStore = useOptionsStore();
const { optionsOrganization, optionsEnable, optionsPerPage }
  = storeToRefs(optionsStore);

const UButton = resolveComponent("UButton");
const columns = computed<TableColumn<User>[]>(() => [

  {
    accessorKey: "username",
    header: () => h("span", t("table.username")),
    cell: ({ row }) => {
      return h("div", { class: "flex items-center gap-3" }, [

        h("div", undefined, [
          h(
            "p",
            { class: "font-medium text-highlighted" },
            row.original.name,
          ),
          h("p", { class: "" }, `${row.original.email}`),
        ]),
      ]);
    },
  },
  {
    accessorKey: "phone",
    header: () => h("span", t("table.phone")),
  },
  {
    accessorKey: "departmentName",
    header: () => h("span", t("table.department")),
  },
  {
    accessorKey: "organizationName",
    header: () => h("span", t("table.organization")),
  },

  {
    accessorKey: "signatoryLevel",
    header: () => h("span", t("table.memberStatus")),
  },
  {
    accessorKey: "createDate",
    header: () => h("span", t("table.createAt")),
  },
  {
    id: "actions",
    cell: ({ row }) => {
      return h("div", { class: "flex justify-end gap-2" }, [
        h(UButton, {
          icon: "i-lucide-edit",
          label: t("edit"),
          color: "success",
          class: "ml-auto",
          to: `/settings/users/members/edit/${row.original.id}`,
        }),
        // h(UButton, {
        //   icon: "i-lucide-trash",
        //   label: t("delete"),
        //   color: "error",
        //   onClick: () => {
        //     // Handle delete action
        //   },
        // }),
      ]);
    },
  },
]);

const search = ref("");
const page = ref(1);
const perPages = ref(10);
const totalRecords = ref(0);
const totalPages = ref(0);

const selectAgency = ref("it");
const selectStatus = ref("active");

const { data, status } = await useApi<User[]>("/api/useraccount.php",
  {
    query: {
      organizationId: profile.value?.organizationId,
      search: search,
      page: page,
      perPages: perPages,
    },
    transform: (response): User[] => {
      if (!response?.status) {
        toast.add({
          title: response.code,
          description: response.message,
          icon: "i-lucide-x",
          progress: false,
          color: "warning",
        });
        return [];
      }
      page.value = response.pagination.page;
      perPages.value = response.pagination.perPages;
      totalRecords.value = response.pagination.totalRecords;
      totalPages.value = response.pagination.totalPages;
      return response.details?.map(item => item) as User[];
    },
  },
);
</script>

<template>
  <div class="flex items-center gap-2 mb-4">
    <UIcon
      name="i-lucide-users"
      class="size-5"
    />
    <h1 class="text-xl">
      {{ t("pageMember.title") }}
    </h1>
  </div>

  <div class="flex flex-col gap-3 md:flex-row md:items-end md:justify-between md:text-end">
    <!-- Left: search + button -->
    <div class="flex w-full flex-col gap-1.5 sm:flex-row sm:items-end">
      <UFormField
        :label="t('formSearch.search')"
        name="search"
        class="w-full sm:w-72"
      >
        <UInput
          v-model="search"
          class="w-full"
        />
      </UFormField>

      <UButton class="self-end sm:self-auto">
        {{ t("formSearch.search") }}
      </UButton>
    </div>

    <div class="flex w-full flex-wrap gap-1.5 justify-end md:w-auto lg:flex-nowrap">
      <UFormField
        :label="t('formSearch.organization')"
        name="organization"
        class="w-full sm:w-40 shrink-0"
      >
        <USelect
          v-model="selectAgency"
          :items="optionsOrganization"
          value-key="value"
          class="w-full"
        />
      </UFormField>

      <UFormField
        :label="t('formSearch.status')"
        name="status"
        class="w-full sm:w-40 shrink-0"
      >
        <USelect
          v-model="selectStatus"
          :items="optionsEnable"
          value-key="value"
          class="w-full"
        />
      </UFormField>

      <UFormField
        :label="t('formSearch.show')"
        name="perPages"
        class="w-20 shrink-0"
      >
        <USelect
          v-model="perPages"
          :items="optionsPerPage"
          class="w-full"
        />
      </UFormField>
    </div>
  </div>

  <div class="w-full z-0">
    <UTable
      :data="data"
      :columns="columns"
      :loading="status === 'pending'"
    />

    <CustomPagination
      v-model="page"
      :total-pages="totalPages"
      :total-records="totalRecords"
      :per-pages="perPages"
      class="mt-4"
    />
  </div>
</template>
