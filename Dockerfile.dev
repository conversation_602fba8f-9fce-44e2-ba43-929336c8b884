FROM oven/bun:1.2-alpine AS development

WORKDIR /app

ENV NODE_ENV=development

ENV NUXT_SESSION_PASSWORD=a-random-password-with-at-least-32-characters
ENV NUXT_UI_PRO_LICENSE=your-nuxt-ui-pro-license

ENV NUXT_PUBLIC_API_BASE=https://supercar-dev.pointit.co.th
ENV NUXT_PUBLIC_API_BASE_REALM=https://iam.pointit.co.th
ENV NUXT_PUBLIC_CLIENT_ID=gw
ENV NUXT_PUBLIC_CLIENT_SECRET=bHgcJjSGgoMEvEAwdoagXWhqWYwTOZAz

COPY package.json bun.lock ./

RUN bun install

COPY . .

EXPOSE 3000


CMD ["bun", "run", "dev"]
