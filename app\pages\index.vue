<script setup lang="ts">
import type { FormFieldProps, FormSubmitEvent, PinInputProps } from "@nuxt/ui";
import { z } from "zod";
import type { ApiResponse, Profile, Token } from "~/types";

definePageMeta({
  layout: "auth",
});

type AuthFormField = FormFieldProps & {
  name: string;
  type?: "checkbox" | "select" | "password" | "text" | "otp";
  defaultValue?: unknown;
  otp?: PinInputProps;
};

const config = useRuntimeConfig();
const profile = useProfileStore();

// const toast = useToast();
// const { login } = useAuth();

// const loginKeyCloak = async () => {
//   try {
//     await login();
//   }
//   catch (error) {
//     console.error("Login failed:", error);
//     toast.add({
//       title: "Uh oh! Something went wrong.",
//       description: String(error) || "Please try again later.",
//       icon: "i-lucide-x",
//       progress: false,
//       color: "error",
//     });
//   }
// };

const fields = ref<AuthFormField[]>([
  {
    name: "username",
    type: "text",
    label: "Username",
    required: true,
    defaultValue: "sign 1",
  },
  {
    name: "password",
    type: "password",
    label: "Password",
    required: true,
    defaultValue: "P@ssw0rd",
  },
]);

const schema = z.object({
  username: z.string().min(2, "Must be at least 2 characters"),
  password: z.string().min(8, "Must be at least 8 characters"),
});

type Schema = z.output<typeof schema>;

const errorMessage = ref<string | null>(null);
const loading = ref<boolean>(false);

async function onSubmit(event: FormSubmitEvent<Schema>) {
  try {
    loading.value = true;
    const data = event.data;

    const payload = {
      username: data.username,
      password: data.password,
      client_id: config.public.clientId,
      client_secret: config.public.clientSecret,
      scope: "openid",
      grant_type: "password",
    };

    const params = new URLSearchParams();
    for (const [key, value] of Object.entries(payload ?? {})) {
      if (value !== undefined && value !== null) {
        params.append(key, String(value));
      }
    }
    const { error: errorKC, data: dataKC } = await useFetch<Token | ApiResponse<Token>>(`/realms/supercar/protocol/openid-connect/token`, {
      method: "POST",
      body: params,
    });

    if (errorKC.value) {
      const err = errorKC.value.data;
      errorMessage.value = err.error_description;
      return;
    }

    const { error: errorSignin, data: dataSignin } = await useFetch<ApiResponse<Profile>>("/api/signin.php", {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${(dataKC.value as Token).access_token}`,
      },
    });

    if (errorSignin.value) {
      const err = errorSignin.value.data;
      errorMessage.value = err.message;
      return;
    }
    if (dataSignin.value?.status === false) {
      errorMessage.value = dataSignin.value.message || "Login failed";
      return;
    }

    profile.setProfile(dataSignin.value?.details as Profile);
    profile.setToken(dataKC.value as Token);

    navigateTo("/dashboard");
  }
  catch (error: unknown) {
    console.error("Submission failed:", (error as Error).message || error);
    // Handle error, e.g., show a toast notification
  }
  finally {
    loading.value = false;
  }
}
</script>

<template>
  <UContainer class="min-h-screen flex items-center justify-center">
    <div class="absolute inset-0 -z-10 bg-[radial-gradient(45rem_50rem_at_top,var(--color-indigo-100),white)] opacity-20" />
    <div class="absolute inset-y-0 right-1/2 -z-10 mr-16 w-[200%] origin-bottom-left skew-x-[-30deg] bg-white shadow-xl ring-1 shadow-indigo-600/10 ring-indigo-50 sm:mr-28 lg:mr-0 xl:mr-16 xl:origin-center" />

    <UCard class="w-full max-w-md">
      <div class="text-center">
        <img
          src="~/assets/images/logo.png"
          alt="Logo"
          class="mx-auto mb-4 w-32 h-32"
        >
        <h1 class="text-xl font-bold">
          ระบบขอใช้ยานพาหนะสถานีอนามัยเฉลิมพระเกียรติฯ
          และโรงพยาบาลส่งเสริมสุขภาพตำบล
        </h1>
      </div>
      <div class="mt-4">
        <UAuthForm
          :schema="schema"
          class="max-w-md"
          :fields="fields"
          :submit="{
            label: 'Submit',
            loading: loading,
          }"
          @submit="onSubmit"
        />
        <UAlert
          v-if="errorMessage"
          class="mt-4"
          color="error"
          :description="errorMessage"
          icon="i-lucide-alert-circle"
        />
      </div>
    </UCard>
  </UContainer>
</template>
