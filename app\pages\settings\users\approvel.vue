<script setup lang="ts">
import type { TableColumn } from "@nuxt/ui";
import type { Approval } from "~/types";

definePageMeta({
  middleware: ["authenticated"],
});

const { t } = useI18n();
const UButton = resolveComponent("UButton");

const isDialogVisible = ref(false);

const data = ref<Approval[]>([
  {
    id: 1,
    name: "john_doe",
    sequence: 1,
    createAt: "2023-01-01",
    createBy: "admin",
  },
  {
    id: 2,
    name: "jane_smith",
    sequence: 2,
    createAt: "2023-01-02",
    createBy: "admin",
  },
]);

const page = ref(1);
const perPages = ref(10);
const totalRecords = ref(0);
const totalPages = ref(0);

const columns = computed<TableColumn<Approval>[]>(() => [
  {
    accessorKey: "name",
    header: () => h("span", t("table.groupName")),
  },
  {
    accessorKey: "sequence",
    header: () => h("div", t("table.sequence")),
  },
  {
    accessorKey: "createAt",
    header: () => h("div", t("table.createAt")),
  },
  {
    accessorKey: "createBy",
    header: () => h("div", t("table.createBy")),
  },
  {
    id: "actions",
    cell: () => {
      return h("div", { class: "flex justify-end gap-2" }, [
        h(UButton, {
          icon: "i-lucide-edit",
          label: t("edit"),
          color: "success",
          class: "",
          onClick: () => {},
        }),
        h(UButton, {
          icon: "i-lucide-trash",
          label: t("delete"),
          color: "error",
          onClick: () => {
            // Handle delete action
          },
        }),
      ]);
    },
  },
]);
</script>

<template>
  <div class="flex items-center gap-2 mb-4">
    <UIcon
      name="fluent-mdl2:reminder-group"
      class="size-5"
    />
    <h1 class="text-xl">
      {{ t("pageApproval.title") }}
    </h1>

    <UButton
      form="memberForm"
      icon="i-lucide-plus"
      :label="t('add')"
      color="info"
      type="submit"
      class="w-fit lg:ms-auto"
      @click="isDialogVisible = true"
    />
  </div>

  <div class="w-full z-0">
    <UTable
      ref="table"
      :data="data"
      :columns="columns"
    />

    <CustomPagination
      v-model="page"
      :total-pages="totalPages"
      :total-records="totalRecords"
      :per-pages="perPages"
      class="mt-4"
    />
  </div>

  <UserApprovelDialog
    v-model:is-dialog-visible="isDialogVisible"
    :approval="undefined"
    @submit="
      () => {
        // Handle submit logic
        isDialogVisible = false;
      }
    "
  />
</template>
