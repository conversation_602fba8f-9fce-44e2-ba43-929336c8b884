<script lang="ts" setup>
import { storeToRefs } from "pinia";
import { useOptionsStore } from "~/stores/options";

definePageMeta({
  middleware: ["authenticated"],
});

const { t } = useI18n();
const optionsStore = useOptionsStore();
const { optionsMailProgram, optionsAuthenticationRequired, optionsSslSupport }
  = storeToRefs(optionsStore);

const showPassword = ref(false);
const form = reactive({
  noreply: "",
  encodeing: "",
  mailProgram: "",

  mailServer: "",
  port: "",
  authenticationRequired: "",
  sslSupport: "",
  username: "",
  password: "",
});
</script>

<template>
  <div class="flex items-center gap-2 mb-4">
    <UIcon
      name="i-lucide-mail"
      class="size-5"
    />
    <h1 class="text-xl">
      {{ t("navSetting.email") }}
    </h1>

    <UButton
      form="settings"
      :label="t('save')"
      color="success"
      type="submit"
      class="w-fit lg:ms-auto"
    />
  </div>

  <!--  general settings -->
  <UPageCard
    variant="subtle"
    :title="t('formEmail.title')"
    class="mb-4"
  >
    <div class="flex flex-row gap-4">
      <UFormField
        :label="t('formEmail.noreply')"
        class="basis-1/2"
      >
        <UInput
          v-model="form.noreply"
          class="w-full"
        />
      </UFormField>

      <UFormField
        :label="t('formEmail.encodeing')"
        class="basis-1/2"
      >
        <UInput
          v-model="form.encodeing"
          class="w-full"
        />
      </UFormField>
    </div>

    <UFormField :label="t('formEmail.mailProgram')">
      <USelect
        v-model="form.mailProgram"
        :items="optionsMailProgram"
        class="w-full"
      />
    </UFormField>
  </UPageCard>

  <UPageCard
    variant="subtle"
    :title="t('formEmail.secondsTitle')"
    class="mb-4"
  >
    <div class="flex flex-row gap-4">
      <UFormField
        :label="t('formEmail.mailServer')"
        class="basis-1/2"
      >
        <UInput
          v-model="form.mailServer"
          class="w-full"
        />
      </UFormField>
      <UFormField
        :label="t('formEmail.port')"
        class="basis-1/2"
      >
        <UInput
          v-model="form.port"
          class="w-full"
        />
      </UFormField>
    </div>

    <div class="flex flex-row gap-4">
      <UFormField
        :label="t('formEmail.authenticationRequired')"
        class="basis-1/2"
      >
        <USelect
          v-model="form.authenticationRequired"
          :items="optionsAuthenticationRequired"
          class="w-full"
        />
      </UFormField>
      <UFormField
        :label="t('formEmail.sslSupport')"
        class="basis-1/2"
      >
        <USelect
          v-model="form.sslSupport"
          :items="optionsSslSupport"
          class="w-full"
        />
      </UFormField>
    </div>

    <div class="flex flex-row gap-4">
      <UFormField
        :label="t('username')"
        class="basis-1/2"
      >
        <UInput
          v-model="form.username"
          class="w-full"
        />
      </UFormField>
      <UFormField
        :label="t('password')"
        class="basis-1/2"
      >
        <UInput
          id="password"
          v-model="form.password"
          class="w-full"
          :type="showPassword ? 'text' : 'password'"
        >
          <template #trailing>
            <UButton
              color="neutral"
              variant="link"
              size="sm"
              :icon="showPassword ? 'i-lucide-eye-off' : 'i-lucide-eye'"
              :aria-label="showPassword ? 'Hide password' : 'Show password'"
              :aria-pressed="showPassword"
              aria-controls="password"
              @click="showPassword = !showPassword"
            />
          </template>
        </UInput>
      </UFormField>
    </div>
  </UPageCard>
</template>
