export type Profile = {
  id: number;
  fullname: string;
  jobTitle: string;
  email: string;
  phone: string;
  organizationId: string;
  organzationName: string;
  username: string;
  signatureLevel: number;
  permissionId: string;
  permissionName: string;
  permission: Permission;
  createDate: string;
};

export type Permission = {
  all: boolean;
  organization: {
    view: boolean;
    create: boolean;
    delete: boolean;
    update: boolean;
  };
  department: {
    view: boolean;
    create: boolean;
    delete: boolean;
    update: boolean;
  };
  permissionRole: {
    view: boolean;
    create: boolean;
    delete: boolean;
    update: boolean;
  };
};

export type Token = {
  "access_token": string;
  "expires_in": number;
  "refresh_expires_in": number;
  "refresh_token": string;
  "token_type": string;
  "id_token": string;
  "not-before-policy": number;
  "session_state": string;
  "scope": string;
};

export type OidcToken = {
  access_token: string;
  expires_at?: number;
  id_token?: string;
  refresh_token?: string;
  scope?: string;
  token_type?: string;
  session_state?: string | null;
};

export type ResponseSignup = {
  details: Profile | null;
  status: boolean;
  tokenDetail: Token | null;
};
