import { defu } from "defu";
import type { UseFetchOptions } from "nuxt/app";
import type { ApiResponse } from "~/types";

export function useApi<T>(
  url: MaybeRefOrGetter<string>,
  options: UseFetchOptions<ApiResponse<T>, T> = {},
) {
  const defaults: UseFetchOptions<ApiResponse<T>, T> = {
    key: toValue(url),
    server: false,
    lazy: true,
    $fetch: useNuxtApp().$api as typeof $fetch,
  };

  // for nice deep defaults, please use unjs/defu
  const params = defu(options, defaults);

  // <ResT, FetchError<CustomError>>
  return useFetch(url, params);
};
