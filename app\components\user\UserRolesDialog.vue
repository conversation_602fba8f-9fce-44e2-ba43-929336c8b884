<script lang="ts" setup>
import type { ApiResponse, Feature, Role } from "~/types";
import type { FormSubmitEvent } from "@nuxt/ui";
import z from "zod";

const { $api } = useNuxtApp();
const { t } = useI18n();
const toast = useToast();
const optionsStore = useOptionsStore();
const {
  optionsOrganization,
} = storeToRefs(optionsStore);

const roleSchema = z.object({
  id: z.string().optional(),
  name: z.string(),
  organizationId: z.string(),
  feature: z.object({
    all: z.number().optional(),
    organization: z.object({
      view: z.boolean().optional(),
      create: z.boolean().optional(),
      delete: z.boolean().optional(),
      update: z.boolean().optional(),
    }),
    department: z.object({
      view: z.boolean().optional(),
      create: z.boolean().optional(),
      delete: z.boolean().optional(),
      update: z.boolean().optional(),
    }),
    permissionRole: z.object({
      view: z.boolean().optional(),
      create: z.boolean().optional(),
      delete: z.boolean().optional(),
      update: z.boolean().optional(),
    }),
  }).optional(),
});

type Schema = z.output<typeof roleSchema>;

interface Props {
  role?: Partial<Schema>;
  isDialogVisible: boolean;
  disable?: boolean;
}
interface Emit {
  (e: "update:isDialogVisible", value: boolean): void;
  (e: "submit", value: Role): void;
}

const props = withDefaults(defineProps<Props>(), {
  role: () => ({
    name: undefined,
    organizationId: undefined,
    feature: undefined,
  }),
  disable: false,
});

const emit = defineEmits<Emit>();

const features = ref<Feature>({
  all: 0,
  organization: {
    view: false,
    create: false,
    delete: false,
    update: false,
  },
  department: {
    view: false,
    create: false,
    delete: false,
    update: false,
  },
  permissionRole: {
    view: false,
    create: false,
    delete: false,
    update: false,
  },
});

const isDialogVisible = ref(props.isDialogVisible);
const state = ref<Partial<Schema>>(structuredClone(toRaw(props.role)));

watch(props, () => {
  state.value = structuredClone(toRaw(props.role));
  isDialogVisible.value = props.isDialogVisible;
  isSelectAll.value = false;

  console.log("props.role?.feature :>> ", props.role?.feature);
  if (props.role?.feature) {
    let all = false;
    features.value = Object.fromEntries(
      Object.entries(features.value).map(([key, value]) => {
        if (key === "all") {
          if (props.role.feature?.[key] === 1) {
            all = true;
          }
          else {
            all = false;
          }
          return [key, props.role.feature?.[key] ?? 0];
        }

        return [
          key,
          {
            ...value,
            view: all ? true : props.role.feature[key]?.view ?? false,
            create: all ? true : props.role.feature[key]?.create ?? false,
            delete: all ? true : props.role.feature[key]?.delete ?? false,
            update: all ? true : props.role.feature[key]?.update ?? false,
          },
        ];
      }),
    );
  }
});

const isSelectAll = ref<boolean | "indeterminate">(false);

const checkedCount = computed(() => {
  let counter = 0;

  Object.entries(features.value).forEach(([key, value]) => {
    if (key !== "all") {
      if (value.view) counter++;
      if (value.create) counter++;
      if (value.delete) counter++;
      if (value.update) counter++;
    }
  });

  return counter;
});

// const isIndeterminate = computed(
//   () =>
//     checkedCount.value > 0 && checkedCount.value < features.value.length * 4,
// );
// select all
watch(isSelectAll, (val) => {
  if (val === "indeterminate") return;
  features.value = Object.fromEntries(
    Object.entries(features.value).map(([key, value]) => {
      if (key === "all") return [key, value];
      return [
        key,
        {
          ...value,
          view: val,
          create: val,
          delete: val,
          update: val,
        },
      ];
    }),
  );
});
// if isSelectAll is true, then set all permissions to true
// watch(isIndeterminate, () => {
//   if (!isIndeterminate.value)
//     isSelectAll.value = false;
// });
// if all features are checked, then set isSelectAll to true
watch(features, () => {
  if (checkedCount.value === 12) {
    isSelectAll.value = true;
    features.value.all = 1;
  }
  else {
    features.value.all = 0;
  }

  if (checkedCount.value > 0 && checkedCount.value < 12) {
    isSelectAll.value = "indeterminate";
    features.value.all = 0;
  }
}, { deep: true });

async function onSubmit(event: FormSubmitEvent<Role>) {
  const { data } = event;

  const response = await $api<ApiResponse<Schema>>("/api/permission.php", {
    method: props.role.id ? "PUT" : "POST",
    query: props.role.id ? { id: props.role.id } : undefined,
    body: JSON.stringify({ ...data, feature: features.value }),
  });
  if (response) {
    toast.add({
      title: response.code,
      description: response.message,
      icon: response.status ? "i-lucide-check" : "i-lucide-x",
      color: response.status ? "success" : "error",
    });
    if (!response.status) {
      return;
    }
  }
  emit("update:isDialogVisible", false);
  emit("submit", data);
  resetForm();
}

const resetForm = () => {
  emit("update:isDialogVisible", false);
  isSelectAll.value = false;
  state.value = structuredClone(toRaw(props.role));
};
</script>

<template>
  <UModal
    v-model:open="isDialogVisible"
    :title="props.role?.name ? t('edit') : t('add')"
    class="max-w-2xl"
    @update:open="(value) => emit('update:isDialogVisible', value)"
  >
    <template #body>
      <UForm
        :schema="roleSchema"
        :state="state"
        class="space-y-4"
        :disabled="props.disable"
        @submit="onSubmit"
      >
        <UFormField
          :label="t('table.roleName')"
          name="name"
        >
          <UInput
            v-model="state.name"
            class="w-full"
          />
        </UFormField>

        <UFormField
          :label="t('organization')"
          name="organizationId"
        >
          <USelectMenu
            v-model="state.organizationId"
            class="w-full"
            :items="optionsOrganization"
            value-key="value"
            label-key="label"
          />
        </UFormField>

        <ul
          role="list"
          class="divide-y divide-default"
        >
          <li class="flex items-center justify-end gap-3 py-3 px-4 sm:px-6">
            <div class="d-flex justify-end">
              <UCheckbox
                v-model="isSelectAll"
                default-value="indeterminate"
                :label="t('all')"
              />
            </div>
          </li>
          <template
            v-for="([key, value]) in Object.entries(features).filter(
              ([k]) => k !== 'all',
            )"
          >
            <li
              v-if="key !== 'all'"
              :key="key"
              class="flex items-center justify-between gap-3 py-3 px-4 sm:px-6"
            >
              <div
                class="flex items-center gap-3 min-w-0"
              >
                <p class="text-highlighted font-medium truncate">
                  {{ t(key) }}
                </p>
              </div>

              <div
                class="flex items-center gap-8"
              >
                <UCheckbox
                  v-model="value.view"
                  :label="t('view')"
                />
                <UCheckbox
                  v-model="value.create"
                  :label="t('create')"
                />
                <UCheckbox
                  v-model="value.delete"
                  :label="t('delete')"
                />
                <UCheckbox
                  v-model="value.update"
                  :label="t('update')"
                />
              <!-- <UCheckbox
                v-model="value.approve"
                :label="t('approve')"
              /> -->
              </div>
            </li>
          </template>
        </ul>

        <div class="flex justify-end gap-2">
          <UButton
            :label="t('cancel')"
            color="neutral"
            variant="subtle"
            @click="resetForm"
          />
          <UButton
            :label="t('save')"
            color="primary"
            variant="solid"
            type="submit"
          />
        </div>
      </UForm>
    </template>
  </UModal>
</template>
