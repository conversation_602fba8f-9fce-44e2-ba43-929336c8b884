import type { Organization } from "./organization";
import type { Role } from "./role";
import type { Feature } from "./feature";
import type { Approval } from "./approvel";
import type { Accessories } from "./accessories";
import type { Booking } from "./bookings";
import type { User } from "./user";

import type { Vehicle } from "./vehicle";
import type { Profile, ResponseSignup, OidcToken, Token } from "./profile";
import type { Model } from "./model";
import type { ApiResponse } from "./apiResponse";
import type { Department } from "./department";

interface Range {
  start: Date;
  end: Date;
}

export {
  ApiResponse,

  Range,
  Organization,
  Role,
  Feature,
  Approval,
  Accessories,
  Booking,
  User,
  Vehicle,
  Profile,
  ResponseSignup,
  Model,
  OidcToken,
  Department,
  Token,
};
