<script setup lang="ts">
const { t } = useI18n();

interface Emit {
  (e: "update:isDialogVisible", value: boolean): void;
  (e: "submit"): void;
}
interface Props {
  callBack: () => Promise<boolean>;
  isDialogVisible: boolean;
}
const props = defineProps<Props>();

const emit = defineEmits<Emit>();

const isDialogVisible = ref(props.isDialogVisible);
watch(props, () => {
  isDialogVisible.value = props.isDialogVisible;
});

async function onSubmit() {
  if (await props.callBack()) {
    emit("update:isDialogVisible", false);
    emit("submit");
  }
}
</script>

<template>
  <UModal
    v-model:open="isDialogVisible"
    :title="t('confirm')"
    @update:open="(value) => emit('update:isDialogVisible', value)"
  >
    <template #body>
      <div class="text-center">
        <UIcon
          name="i-lucide-alert-triangle"
          class="size-10 text-warning"
        />
        <p class="mt-4">
          {{ t("confirmUpdate") }}
        </p>
      </div>
      <slot name="body" />
      <div
        class="flex justify-end gap-2 mt-4"
      >
        <UButton
          :label="t('cancel')"
          color="neutral"
          variant="subtle"
          @click="emit('update:isDialogVisible', false)"
        />
        <UButton
          :label="t('confirm')"
          color="success"
          variant="solid"
          loading-auto
          @click="onSubmit"
        />
      </div>
    </template>
  </UModal>
</template>
