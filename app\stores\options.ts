export const useOptionsStore = defineStore("optionsStore", () => {
  const { t } = useI18n();

  type OptionServer = {
    id: string;
    title: string;
  };
  type OptionItem = {
    value: string | number;
    label: string;
  };
  type ResponseOptions = {
    drivers?: OptionServer[];
    listProvince?: OptionServer[];
    listVehicle?: OptionServer[];
    listVehicleModel?: OptionServer[];
    listVehicleModelOptions?: {
      brand?: OptionServer[];
      type?: OptionServer[];
      color?: OptionServer[];
      engine?: OptionServer[];
      seat?: OptionServer[];
    };
    listLocation?: OptionServer[];
  };

  const optionsPerPage = ref([10, 20, 30, 50, 100]);
  const optionsModelYear = ref(Array.from({ length: 20 }, (_, i) => ({
    value: new Date().getFullYear() - i,
    label: (new Date().getFullYear() - i).toString(),
  })));
  const optionsVehicle = ref<OptionItem[]>([]);
  const optionsType = ref<OptionItem[]>([]);
  const optionsBrand = ref<OptionItem[]>([]);
  const optionsOrganization = ref<OptionItem[]>([]);
  const optionsStatus = computed(() => [
    { label: t("statusOptions.all"), value: "all" },
    { label: t("statusOptions.pending"), value: "pending" },
    { label: t("statusOptions.approved"), value: "confirmed" },
    { label: t("statusOptions.notAllow"), value: "cancelled" },
    { label: t("statusOptions.cancelByCustomer"), value: "cancelByCustomer" },
    { label: t("statusOptions.cancelByStaff"), value: "cancelByStaff" },
  ]);
  const optionsDriver = ref<OptionItem[]>([]);
  const optionsEnable = computed(() => [
    { label: t("enableOptions.enable"), value: "enable" },
    { label: t("enableOptions.disable"), value: "disable" },
  ]);

  const optionsLoginBy = computed(() => [
    {
      label: t("username"),
      value: "username",
    },
    {
      label: t("email"),
      value: "email",
    },
    {
      label: t("phone"),
      value: "phone",
    },
    {
      label: t("idCard"),
      value: "idCard",
    },
  ]);

  const optionsSex = computed(() => [
    { label: t("formUser.sexOptions.notSet"), value: "notSet" },
    { label: t("formUser.sexOptions.male"), value: "male" },
    { label: t("formUser.sexOptions.female"), value: "female" },
  ]);
  const optionsProvince = ref<OptionItem[]>([]);
  const optionsVehicleModel = ref<OptionItem[]>([]);
  const optionsColor = ref<OptionItem[]>([]);
  const optionsEngine = ref<OptionItem[]>([]);
  const optionsSeat = ref<OptionItem[]>([]);
  const optionsLocation = ref<OptionItem[]>([]);
  const optionsPermission = ref<OptionItem[]>([]);

  const mapValueOptions = (options: OptionServer[]) => {
    return options.map(option => ({
      value: option.id,
      label: option.title,
    }));
  };

  async function initialOptions() {
    const { data, error } = await useAsyncData("options", async () => {
      const [options, location, vehicle, organization, permission] = await Promise.all([
        useNuxtApp().$api("/api/options.php?list=all"),
        useNuxtApp().$api("/api/options.php?list=location&locationId=dde8bec2-327ad77d-686cb4db-42e6d811&level=1"),
        useNuxtApp().$api("/api/options.php?list=vehicle"),
        useNuxtApp().$api("/api/organization.php?page=1&perPages=100000"),
        useNuxtApp().$api("/api/permission.php?page=1&perPages=100000"),
      ]);

      return { options, location, vehicle, organization, permission };
    });

    if (error.value) {
      console.error("Error fetching options:", error.value);
      return;
    }

    if (data.value) {
      const options = data.value.options as ResponseOptions;
      // optionsDriver.value = mapValueOptions(options.drivers || []);
      optionsProvince.value = mapValueOptions(options?.listProvince || []);
      optionsVehicleModel.value = mapValueOptions(options?.listVehicleModel || []);
      optionsBrand.value = mapValueOptions(options?.listVehicleModelOptions?.brand || []);
      optionsType.value = mapValueOptions(options?.listVehicleModelOptions?.type || []);
      optionsColor.value = mapValueOptions(options?.listVehicleModelOptions?.color || []);
      optionsEngine.value = mapValueOptions(options?.listVehicleModelOptions?.engine || []);
      optionsSeat.value = mapValueOptions(options?.listVehicleModelOptions?.seat || []);

      const locations = options?.listLocation || [] as OptionServer[];
      optionsLocation.value = mapValueOptions(locations || []);

      const vehicles = data.value.vehicle || [] as ResponseOptions;
      optionsVehicle.value = mapValueOptions(vehicles.listVehicle || []);

      const organization = data.value.organization.details as OptionServer[];
      optionsOrganization.value = organization?.map(org => ({
        value: org.id,
        label: org?.name,
      }));

      const permission = data.value.permission.details as OptionServer[];
      optionsPermission.value = permission?.map(perm => ({
        value: perm.id,
        label: perm?.name,
      }));
    }

    // const { data: location, error: locationError } = await useApi<OptionServer[]>("/api/options.php?list=location&locationId=dde8bec2-327ad77d-686cb4db-42e6d811&level=1", {
    //   method: "GET",
    // });

    // if (locationError.value) {
    //   console.error("Error fetching options:", locationError.value);
    //   return;
    // }

    // if (location.value) {
    //   optionsLocation.value = mapValueOptions(location.value || []);
    // }

    // const { data: vehicle, error: vehicleError } = await useApi<ResponseOptions>("/api/options.php?list=vehicle", {
    //   method: "GET",
    // });

    // if (vehicleError.value) {
    //   console.error("Error fetching vehicle options:", vehicleError.value);
    //   return;
    // }

    // if (vehicle.value) {
    //   optionsVehicle.value = mapValueOptions(vehicle.value.listVehicle[0] || []);
    // }
  }

  return {
    optionsPerPage,
    optionsVehicle,
    optionsType,
    optionsBrand,
    optionsOrganization,
    optionsStatus,
    optionsDriver,
    optionsEnable,
    optionsPermission,
    optionsLoginBy,
    optionsSex,
    initialOptions,
    optionsModelYear,
    optionsProvince,
    optionsVehicleModel,
    optionsColor,
    optionsSeat,
    optionsEngine,
    optionsLocation,
  };
});
