<script setup lang="ts">
import { storeToRefs } from "pinia";
import type { TableColumn } from "@nuxt/ui";
import { sub } from "date-fns";
import { h, resolveComponent } from "vue";
import type { Range, Booking } from "~/types";
import { useOptionsStore } from "~/stores/options";

definePageMeta({
  middleware: ["authenticated"],
});

const { t } = useI18n();
const optionsStore = useOptionsStore();
const {
  optionsVehicle,
  optionsStatus,
  optionsOrganization,
  optionsPerPage,
} = storeToRefs(optionsStore);

const UCheckbox = resolveComponent("UCheckbox");
const UBadge = resolveComponent("UBadge");
const search = ref("");

const data = ref<Booking[]>([
  {
    description: "Booking 1",
    vehicle: "Car A",
    status: "Confirmed",
    reason: "No special requests",
    contactName: "John Doe",
    chauffeur: "Chauffeur A",
    createAt: new Date().toISOString(),
  },
  {
    description: "Booking 2",
    vehicle: "Car B",
    status: "Pending",
    reason: "Requires confirmation",
    contactName: "<PERSON> Smith",
    chauffeur: "Chauffeur B",
    createAt: new Date().toISOString(),
  },
  {
    description: "Booking 3",
    vehicle: "Car C",
    status: "Cancelled",
    reason: "Customer requested cancellation",
    contactName: "Alice Johnson",
    chauffeur: "Chauffeur C",
    createAt: new Date().toISOString(),
  },
]);

const columns = computed<TableColumn<Booking>[]>(() => [
  {
    id: "select",
    header: ({ table }) =>
      h(UCheckbox, {
        "modelValue": table.getIsSomePageRowsSelected()
          ? "indeterminate"
          : table.getIsAllPageRowsSelected(),
        "onUpdate:modelValue": (value: boolean | "indeterminate") =>
          table.toggleAllPageRowsSelected(!!value),
        "aria-label": "Select all",
      }),
    cell: ({ row }) =>
      h(UCheckbox, {
        "modelValue": row.getIsSelected(),
        "onUpdate:modelValue": (value: boolean | "indeterminate") =>
          row.toggleSelected(!!value),
        "aria-label": "Select row",
      }),
  },
  {
    accessorKey: "description",
    header: () => h("span", t("table.usageDetails")),
  },
  {
    accessorKey: "vehicle",
    header: () => h("span", t("table.vehicle")),
  },
  {
    accessorKey: "contactName",
    header: () => h("span", t("table.contactName")),
  },
  {
    accessorKey: "chauffeur",
    header: () => h("span", t("table.chauffeur")),
  },
  {
    accessorKey: "createAt",
    header: () => h("span", t("table.createAt")),
    cell: ({ row }) => {
      const date = new Date(row.getValue("createAt"));
      return h("span", date.toLocaleDateString());
    },
  },
  {
    accessorKey: "status",
    header: () => h("span", t("table.status")),
    cell: ({ row }) => {
      const status = row.getValue("status");

      const color = {
        Pending: "success" as const,
        Cancelled: "error" as const,
        Confirmed: "neutral" as const,
      }[row.getValue("status") as string];

      return h(UBadge, { variant: "subtle", color }, () => status);
    },
  },
  {
    accessorKey: "reason",
    header: () => h("span", t("table.reason")),
  },
]);

const table = useTemplateRef("table");
const rowSelection = ref();
const page = ref(1);
const perPages = ref(10);
const totalRecords = ref(0);
const totalPages = ref(0);

const selectVehicle = ref("all");
const selectStatus = ref("all");
const selectAgency = ref("it");

const range = shallowRef<Range>({
  start: sub(new Date(), { days: 14 }),
  end: new Date(),
});
</script>

<template>
  <div class="flex items-center gap-2 py-2">
    <UIcon
      name="i-lucide-clipboard-list"
      class="size-5"
    />
    <h1 class="text-xl">
      {{ t("pageReport.title") }}
    </h1>
  </div>

  <div class="flex flex-col gap-3 md:flex-row md:items-end md:justify-between md:text-end">
    <div class="flex w-full flex-col gap-1.5 sm:flex-row sm:items-end">
      <UFormField
        :label="t('formSearch.search')"
        name="search"
        class="w-full sm:w-72"
      >
        <UInput
          v-model="search"
          class="w-full"
        />
      </UFormField>

      <UButton class="self-end sm:self-auto">
        {{ t("formSearch.search") }}
      </UButton>
    </div>
    <div class="flex w-full flex-wrap gap-1.5 justify-end md:w-auto lg:flex-nowrap">
      <UFormField
        :label="t('formSearch.startDate')"
        name="dateRange"
        class="w-full sm:w-60 shrink-0"
      >
        <DateRangePicker
          v-model="range"
          class="w-full"
        />
      </UFormField>

      <UFormField
        :label="t('formSearch.vehicle')"
        name="vehicle"
        class="w-full sm:w-40 shrink-0"
      >
        <USelect
          v-model="selectVehicle"
          :items="optionsVehicle"
          value-key="value"
          class="w-full"
        />
      </UFormField>

      <UFormField
        :label="t('formSearch.organization')"
        name="organization"
        class="w-full sm:w-40 shrink-0"
      >
        <USelect
          v-model="selectAgency"
          :items="optionsOrganization"
          value-key="value"
          class="w-full"
        />
      </UFormField>

      <UFormField
        :label="t('formSearch.status')"
        name="status"
        class="w-full sm:w-40 shrink-0"
      >
        <USelect
          v-model="selectStatus"
          :items="optionsStatus"
          value-key="value"
          class="w-full"
        />
      </UFormField>

      <UFormField
        :label="t('formSearch.show')"
        name="perPages"
        class="w-20 shrink-0"
      >
        <USelect
          v-model="perPages"
          :items="optionsPerPage"
          class="w-full"
        />
      </UFormField>
    </div>
  </div>

  <div class="w-full z-0">
    <UTable
      ref="table"
      v-model:row-selection="rowSelection"
      :data="data"
      :columns="columns"
    />

    <CustomPagination
      v-model="page"
      :total-pages="totalPages"
      :total-records="totalRecords"
      :per-pages="perPages"
      class="mt-4"
    />
  </div>
</template>
