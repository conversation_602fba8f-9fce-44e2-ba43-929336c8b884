<script lang="ts" setup>
definePageMeta({
  middleware: ["authenticated"],
});

const { t } = useI18n();

const form = ref({
  email: false,
});

async function onChange() {
  // Do something with data
  console.log(form.value.email);
}
</script>

<template>
  <div class="flex items-center gap-2 mb-4">
    <UIcon
      name="i-lucide-bell"
      class="size-5"
    />
    <h1 class="text-xl">
      {{ t("navSetting.notification") }}
    </h1>
  </div>

  <UPageCard
    variant="subtle"
    :title="t('pageNotification.title')"
    class="mb-4"
  >
    <UFormField
      :label="t('pageNotification.email')"
      class="flex items-center justify-between not-last:pb-4 gap-2"
    >
      <USwitch
        v-model="form.email"
        @update:model-value="onChange"
      />
    </UFormField>
  </UPageCard>
</template>
