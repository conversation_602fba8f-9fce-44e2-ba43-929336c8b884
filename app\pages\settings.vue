<script setup lang="ts">
import type { NavigationMenuItem } from "@nuxt/ui";
import { useBreakpoints } from "@vueuse/core";

const breakpoints = useBreakpoints({
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
});

const isMobile = breakpoints.smaller("md"); // < 768px

definePageMeta({
  middleware: ["authenticated"],
});

const { t } = useI18n();
const route = useRoute();
const { initialOptions } = useOptionsStore();

const items = computed<NavigationMenuItem[]>(() => [
  // {
  //   label: t("navSetting.site"),
  //   icon: "i-lucide-globe",
  //   to: "/settings",
  //   exact: true,
  // },
  {
    label: t("navSetting.organization"),
    icon: "i-codicon-organization",
    to: "/settings/organization",
  },
  {
    label: t("navSetting.department"),
    icon: "i-mingcute-department-fill",
    to: "/settings/department",
  },
  {
    label: t("nav.users.main"),
    icon: "i-lucide-users",
    type: "trigger",
    active: route.path.startsWith("/settings/users"),
    children: [
      {
        label: t("nav.users.all"),
        to: "/settings/users/members",
      },
      {
        label: t("nav.users.role"),
        to: "/settings/users/roles",
      },
      // {
      //   label: t("nav.users.approvel"),
      //   to: "/settings/users/approvel",
      // },
    ],
  },
  // {
  //   label: t("navSetting.email"),
  //   icon: "i-lucide-mail",
  //   to: "/settings/email",
  // },
  // {
  //   label: t("navSetting.notification"),
  //   icon: "i-lucide-bell",
  //   to: "/settings/notification",
  // },
  {
    label: t("navSetting.vehicle.title"),
    icon: "mdi:car-cog",
    active: route.path.startsWith("/settings/vehicle"),
    children: [
      {
        label: t("navSetting.vehicle.list"),
        to: "/settings/vehicle/list",
      },
      {
        label: t("navSetting.vehicle.brand"),
        to: "/settings/vehicle/model",
      },
      // {
      //   label: t("navSetting.vehicle.types"),
      //   to: "/settings/vehicle/types",
      // },

      // {
      //   label: t("navSetting.vehicle.accessories"),
      //   to: "/settings/vehicle/accessories",
      // },
    ],
  },
]);

await callOnce(() => initialOptions());
</script>

<template>
  <!-- <UDashboardPanel id="settings">
    <div class="grid grid-cols-6 gap-4">
      <div class="col-span-1">
        <UDashboardSidebar
          resizable
          :ui="{ body: 'h-70 px-2' }"
        >
          <template #default="{}">
            <UNavigationMenu
              :items="items"
              orientation="vertical"
            />
          </template>
        </UDashboardSidebar>
      </div>
      <div class="col-span-5 p-4">
        <NuxtPage />
      </div>
    </div>
  </UDashboardPanel> -->
  <div class="grid grid-cols-6 gap-4">
    <div class="col-auto">
      <UNavigationMenu
        :collapsed="isMobile"
        popover
        :items="items"
        orientation="vertical"
      />
    </div>
    <div class="col-span-5 p-0 md:p-4">
      <NuxtPage />
    </div>
  </div>
</template>
