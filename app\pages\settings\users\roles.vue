<script setup lang="ts">
import type { TableColumn } from "@nuxt/ui";
import type { Role } from "~/types";

definePageMeta({
  middleware: ["authenticated"],
});

const { t } = useI18n();
const toast = useToast();
const UButton = resolveComponent("UButton");
const optionsStore = useOptionsStore();
const { optionsPerPage } = storeToRefs(optionsStore);

const isDialogVisible = ref(false);
const isDeleteDialogVisible = ref(false);

const search = ref("");
const page = ref(1);
const perPages = ref(10);
const totalRecords = ref(0);
const totalPages = ref(0);
const isDisable = ref(false);
const editedItem = ref<Role | undefined>(undefined);
const deletePath = ref<string | undefined>(undefined);

const columns = computed<TableColumn<Role>[]>(() => [
  {
    accessorKey: "name",
    header: () => h("span", t("table.roleName")),
  },
  {
    accessorKey: "organizationName",
    header: () => h("span", t("table.organization")),
  },
  {
    accessorKey: "createDate",
    header: () => h("span", t("table.createAt")),
  },
  {
    id: "actions",
    cell: ({ row }) => {
      return h("div", { class: "flex justify-end gap-2" }, [
        h(UButton, {
          icon: "i-lucide-eye",
          label: t("detail"),
          color: "info",
          class: "",
          onClick: () => {
            isDisable.value = true;
            editedItem.value = row.original;
            isDialogVisible.value = true;
          },
        }),
        h(UButton, {
          icon: "i-lucide-edit",
          label: t("edit"),
          color: "success",
          class: "",
          onClick: () => {
            isDisable.value = false;
            isDialogVisible.value = true;
            editedItem.value = row.original;
          },
        }),
        h(UButton, {
          icon: "i-lucide-trash",
          label: t("delete"),
          color: "error",
          onClick: () => {
            deletePath.value = `/api/permission.php?id=${row.original.id}`;
            isDeleteDialogVisible.value = true;
          },
        }),
      ]);
    },
  },
]);

const { data, status, refresh } = await useApi<Role[]>("/api/permission.php",
  {
    query: {
      search: search,
      page: page,
      perPages: perPages,
    },
    transform: (response): Role[] => {
      if (!response?.status) {
        toast.add({
          title: response.code,
          description: response.message,
          icon: "i-lucide-x",
          progress: false,
          color: "warning",
        });
        return [];
      }
      page.value = response.pagination.page;
      perPages.value = response.pagination.perPages;
      totalRecords.value = response.pagination.totalRecords;
      totalPages.value = response.pagination.totalPages;
      return response.details?.map(item => item) as Role[];
    },
  },
);
</script>

<template>
  <div class="flex items-center gap-2 mb-4">
    <UIcon
      name="i-lucide-star"
      class="size-5"
    />
    <h1 class="text-xl">
      {{ t("pageRole.title") }}
    </h1>

    <UButton
      form="memberForm"
      icon="i-lucide-plus"
      :label="t('add')"
      color="info"
      type="submit"
      class="w-fit lg:ms-auto"
      @click="editedItem = undefined; isDialogVisible = true"
    />
  </div>

  <div class="flex flex-col gap-3 md:flex-row md:items-end md:justify-between md:text-end">
    <div class="flex w-full flex-row gap-1.5 sm:flex-row sm:items-end">
      <UFormField
        :label="t('formSearch.search')"
        name="search"
        class="w-full sm:w-72"
      >
        <UInput
          v-model="search"
          class="w-full"
        />
      </UFormField>

      <UButton class="self-end sm:self-auto">
        {{ t("formSearch.search") }}
      </UButton>
    </div>
    <div class="flex w-full flex-wrap gap-1.5 justify-end md:w-auto lg:flex-nowrap">
      <UFormField
        :label="t('formSearch.show')"
        name="pageSize"
        class="w-20 shrink-0"
      >
        <USelect
          v-model="perPages"
          :items="optionsPerPage"
          class="w-full"
        />
      </UFormField>
    </div>
  </div>
  <div class="w-full z-0">
    <UTable
      ref="table"
      :data="data"
      :columns="columns"
      :loading="status === 'pending'"
    />

    <CustomPagination
      v-model="page"
      :total-pages="totalPages"
      :total-records="totalRecords"
      :per-pages="perPages"
      class="mt-4"
    />
  </div>

  <UserRolesDialog
    v-model:is-dialog-visible="isDialogVisible"
    :role="editedItem"
    :disable="isDisable"
    @submit="refresh()"
  />

  <DeleteDialog
    v-model:is-dialog-visible="isDeleteDialogVisible"
    :url="deletePath"
    @submit="refresh()"
  />
</template>
