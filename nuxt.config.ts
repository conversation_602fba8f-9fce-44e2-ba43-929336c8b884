// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  modules: [
    "@nuxt/ui-pro",
    "@nuxtjs/i18n",
    "@nuxt/eslint",
    "@pinia/nuxt",
    "pinia-plugin-persistedstate/nuxt",
  ],

  ssr: false,

  components: [
    {
      path: "~/components",
      pathPrefix: false,
    },
  ],
  devtools: { enabled: true },
  css: ["~/assets/css/main.css"],
  ui: {
    theme: {
      colors: [
        "primary",
        "secondary",
        "info",
        "success",
        "warning",
        "error",
        "neutral",
        "BLACK", // Standard Black
        "WHITE", // Standard White
        "RED", // Standard Red (usually the default red)
        "YELLOW", // Standard Yellow
        "GREEN", // Standard Green
        "BLUE", // Standard Blue
        "INDIGO", // Standard Indigo
        "PURPLE", // Standard Purple
        "PINK", // Standard Pink
        "GRAY", // Standard Gray
        "SLATE", // Slate is a neutral grayish-blue
        "ZINC", // Zinc is a neutral gray with a cool undertone
        "STONE", // Stone (a gray-brown color)
        "LIME", // Standard Lime
        "AMBER", // Standard Amber
        "ORANGE", // Standard Orange
        "FUCHSIA", // Standard Fuchsia
        "CYAN", // Standard Cyan
        "TEAL", // Standard Teal
        "EMERALD", // Emerald green
        "VIOLET", // Standard Violet
        "ROSE", // Rose color (usually a soft red/pink)
      ],
    },
  },

  runtimeConfig: {
    public: {
      apiBase: process.env.NUXT_PUBLIC_API_BASE ?? "",
      apiBaseRealm: process.env.NUXT_PUBLIC_API_BASE_REALM ?? "",
      clientId: process.env.NUXT_PUBLIC_CLIENT_ID ?? "",
      clientSecret: process.env.NUXT_PUBLIC_CLIENT_SECRET ?? "",
    },
  },

  build: {
    transpile: ["@vuepic/vue-datepicker"],
  },

  compatibilityDate: "2025-07-18",

  vite: {
    server: {
      proxy: {
        "/api": {
          target: process.env.NUXT_PUBLIC_API_BASE ?? "",
          changeOrigin: true,
          secure: false,
        },
        "/realms/": {
          target: process.env.NUXT_PUBLIC_API_BASE_REALM ?? "",
          changeOrigin: true,
          secure: false,
        },
      },
      watch: {
        usePolling: true,
      },

    },
  },

  typescript: {
    typeCheck: true,
    strict: true,
  },

  eslint: {
    config: {
      stylistic: {
        quotes: "double",
        semi: true,
      },
    },
  },

  i18n: {
    defaultLocale: "th",
    strategy: "no_prefix",
    lazy: true,
    langDir: "lang/",
    detectBrowserLanguage: {
      useCookie: true,
      cookieKey: "i18n_redirected",
      fallbackLocale: "en",
      redirectOn: "root", // recommended
    },
    locales: [{
      code: "th",
      name: "TH",
      file: "th.ts",
    }, {
      code: "en",
      name: "EN",
      file: "en.ts",
    }],
    bundle: {
      optimizeTranslationDirective: false,
    },
  },
});
