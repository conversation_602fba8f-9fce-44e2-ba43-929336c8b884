<script lang="ts" setup>
import * as z from "zod";
import type { FormSubmitEvent } from "@nuxt/ui";
import type { Approval } from "~/types";

const { t } = useI18n();

const approvalSchema = z.object({
  name: z.string(),
  sequence: z.number(),
});

interface Props {
  approval?: Approval;
  isDialogVisible: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  approval: () => ({
    id: 0,
    name: null,
    sequence: 0,
    createAt: new Date().toISOString(),
    createBy: "system",
  }),
});
interface Emit {
  (e: "update:isDialogVisible", value: boolean): void;
  (e: "submit", value: Approval): void;
}
const emit = defineEmits<Emit>();

let state = reactive<Approval>(structuredClone(toRaw(props.approval)));

watch(props, () => {
  state = structuredClone(toRaw(props.approval));
});

async function onSubmit(event: FormSubmitEvent<Approval>) {
  const { data } = event;
  emit("update:isDialogVisible", false);
  emit("submit", data);
}

const resetForm = () => {
  emit("update:isDialogVisible", false);
  state = structuredClone(toRaw(props.approval));
};
</script>

<template>
  <UModal
    v-model:open="props.isDialogVisible"
    @update:open="(value) => emit('update:isDialogVisible', value)"
    :title="props.approval?.name ? t('edit') : t('add')"
  >
    <template #body>
      <UForm
        :schema="approvalSchema"
        :state="state"
        class="space-y-4"
        @submit="onSubmit"
      >
        <UFormField :label="t('table.agencyName')" name="name">
          <UInput v-model="state.name" class="w-full" />
        </UFormField>

        <UFormField :label="t('table.sequence')" name="sequence">
          <UInputNumber
            v-model="state.sequence"
            orientation="vertical"
            class="w-full"
          />
        </UFormField>

        <div class="flex justify-end gap-2">
          <UButton
            :label="t('cancel')"
            color="neutral"
            variant="subtle"
            @click="resetForm"
          />
          <UButton
            :label="t('save')"
            color="primary"
            variant="solid"
            type="submit"
          />
        </div>
      </UForm>
    </template>
  </UModal>
</template>
