<script lang="ts" setup>
import { storeToRefs } from "pinia";
import type { TableColumn } from "@nuxt/ui";
import { h, resolveComponent } from "vue";
import type { Department } from "~/types";
import { useOptionsStore } from "~/stores/options";

definePageMeta({
  middleware: ["authenticated"],
});

const toast = useToast();
const { t } = useI18n();
const optionsStore = useOptionsStore();
const { optionsPerPage } = storeToRefs(optionsStore);
const UButton = resolveComponent("UButton");

const isDialogVisible = ref(false);
const isDeleteDialogVisible = ref(false);
const editedItem = ref<Department | undefined>(undefined);
const deletePath = ref<string | undefined>(undefined);

const search = ref("");
const page = ref(1);
const perPages = ref(10);
const totalRecords = ref(0);
const totalPages = ref(0);

const columns = computed<TableColumn<Department>[]>(() => [
  {
    accessorKey: "name",
    header: () => h("span", t("table.name")),
  },
  {
    accessorKey: "organizationName",
    header: () => h("span", t("table.organization")),

  },
  {
    accessorKey: "canDelete",
    header: () => h("span", t("delete")),
  },
  {
    accessorKey: "canEdit",
    header: () => h("span", t("edit")),
  },
  {
    accessorKey: "createDate",
    header: () => h("span", t("table.createAt")),
  },

  {
    id: "actions",
    cell: ({ row }) => {
      return h("div", { class: "text-right flex gap-2" }, [
        h(UButton, {
          icon: "i-lucide-edit",
          label: t("edit"),
          color: "success",
          class: "ml-auto",
          onClick: () => {
            const organization = row.original as Department;
            openEditOrganizationDialog(organization);
          },
        }),
        h(UButton, {
          icon: "i-lucide-trash",
          label: t("delete"),
          color: "error",
          onClick: () => {
            deletePath.value = `/api/department.php?id=${row.original.id}`;
            isDeleteDialogVisible.value = true;
          },
        }),
      ]);
    },
  },
]);

const openEditOrganizationDialog = (organization: Department) => {
  editedItem.value = organization;

  isDialogVisible.value = true;
};

const { data, status, refresh } = await useApi<Department[]>("/api/department.php",
  {
    query: {
      search: search,
      page: page,
      perPages: perPages,
    },
    transform: (response): Department[] => {
      if (!response?.status) {
        toast.add({
          title: response.code,
          description: response.message,
          icon: "i-lucide-x",
          progress: false,
          color: "warning",
        });
        return [];
      }
      page.value = response.pagination.page;
      perPages.value = response.pagination.perPages;
      totalRecords.value = response.pagination.totalRecords;
      totalPages.value = response.pagination.totalPages;
      return response.details?.map(item => item) as Department[];
    },
  },
);

const onSubmit = () => {
  editedItem.value = undefined;
  deletePath.value = undefined;
  isDialogVisible.value = false;
  isDeleteDialogVisible.value = false;
  refresh();
};
</script>

<template>
  <div class="flex items-center gap-2 mb-4">
    <UIcon
      name="i-mingcute-department-fill"
      class="size-5"
    />
    <h1 class="text-xl">
      {{ t("navSetting.department") }}
    </h1>

    <UButton
      icon="i-lucide-plus"
      form="settings"
      :label="t('add')"
      color="info"
      type="submit"
      class="w-fit lg:ms-auto"
      @click="editedItem = undefined, isDialogVisible = true"
    />
  </div>

  <div class="flex flex-col gap-3 md:flex-row md:items-end md:justify-between md:text-end">
    <div class="flex w-full flex-row gap-1.5 sm:flex-row sm:items-end">
      <UFormField
        :label="t('formSearch.search')"
        name="search"
        class="w-full sm:w-72"
      >
        <UInput
          v-model="search"
          class="w-full"
        />
      </UFormField>

      <UButton class="self-end sm:self-auto">
        {{ t("formSearch.search") }}
      </UButton>
    </div>
    <div class="flex w-full flex-wrap gap-1.5 justify-end md:w-auto lg:flex-nowrap">
      <UFormField
        :label="t('formSearch.show')"
        name="pageSize"
        class="w-20 shrink-0"
      >
        <USelect
          v-model="perPages"
          :items="optionsPerPage"
          class="w-full"
        />
      </UFormField>
    </div>
  </div>

  <div class="w-full z-0">
    <UTable
      ref="table"
      :data="data"
      :columns="columns"
      :loading="status === 'pending'"
    />

    <CustomPagination
      v-model="page"
      :total-pages="totalPages"
      :total-records="totalRecords"
      :per-pages="perPages"
      class="mt-4"
    />
  </div>

  <DepartmentDialog
    v-model:is-dialog-visible="isDialogVisible"
    :department="editedItem"
    @submit="onSubmit"
  />

  <DeleteDialog
    v-model:is-dialog-visible="isDeleteDialogVisible"
    :url="deletePath"
    @submit="onSubmit"
  />
</template>
