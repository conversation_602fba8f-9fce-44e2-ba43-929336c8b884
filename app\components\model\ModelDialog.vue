<script lang="ts" setup>
import * as z from "zod";
import type { FormSubmitEvent } from "@nuxt/ui";
import { useOptionsStore } from "~/stores/options";
import type { ApiResponse } from "~/types";

const { $api } = useNuxtApp();
const { t } = useI18n();
const toast = useToast();
const optionsStore = useOptionsStore();
const {
  optionsBrand,
  optionsType,
  optionsEngine,
  optionsSeat,
  optionsModelYear,
} = storeToRefs(optionsStore);

const modelSchema = z.object({
  id: z.string().optional(),
  brand: z.string(),
  model: z.string(),
  engine: z.string(),
  type: z.string(),
  seat: z.number(),
  year: z.number().int().max(new Date().getFullYear()),
});
type Schema = z.output<typeof modelSchema>;
interface Props {
  model?: Partial<Schema>;
  isDialogVisible: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  model: () => ({
    id: undefined,
    brand: undefined,
    model: undefined,
    engine: undefined,
    type: undefined,
    seat: 2,
    year: new Date().getFullYear(),
  }),
});
interface Emit {
  (e: "update:isDialogVisible", value: boolean): void;
  (e: "submit", value: Schema): void;
}
const emit = defineEmits<Emit>();

const isDialogVisible = ref(props.isDialogVisible);
let state = reactive<Partial<Schema>>(structuredClone(toRaw(props.model)));

watch(props, () => {
  state = structuredClone(toRaw(props.model));
  isDialogVisible.value = props.isDialogVisible;
});

async function onSubmit(event: FormSubmitEvent<Schema>) {
  const { data } = event;

  const response = await $api<ApiResponse<Schema>>("/api/model.php", {
    method: props.model.id ? "PUT" : "POST",
    query: props.model.id ? { id: props.model.id } : undefined,
    body: JSON.stringify(data),
  });
  if (response) {
    toast.add({
      title: response.code,
      description: response.message,
      icon: response.status ? "i-lucide-check" : "i-lucide-x",
      color: response.status ? "success" : "error",
    });
    if (!response.status) {
      return;
    }
  }

  emit("update:isDialogVisible", false);
  emit("submit", data);
}

const resetForm = () => {
  emit("update:isDialogVisible", false);
  state = structuredClone(toRaw(props.model));
};
</script>

<template>
  <UModal
    v-model:open="isDialogVisible"
    :title="props.model?.id ? t('edit') : t('add')"
    @update:open="(value: boolean) => emit('update:isDialogVisible', value)"
  >
    <template #body>
      <UForm
        :schema="modelSchema"
        :state="state"
        class="space-y-4"
        @submit="onSubmit"
      >
        <UFormField
          :label="t('table.brand')"
          name="brand"
        >
          <USelectMenu
            v-model="state.brand"
            class="w-full"
            :items="optionsBrand"
            value-key="value"
            label-key="label"
          />
        </UFormField>

        <UFormField
          :label="t('table.model')"
          name="model"
        >
          <UInput
            v-model="state.model"
            class="w-full"
          />
        </UFormField>

        <UFormField
          :label="t('table.engine')"
          name="engine"
        >
          <USelectMenu
            v-model="state.engine"
            class="w-full"
            :items="optionsEngine"
            value-key="value"
            label-key="label"
          />
        </UFormField>

        <UFormField
          :label="t('table.type')"
          name="type"
        >
          <USelectMenu
            v-model="state.type"
            class="w-full"
            :items="optionsType"
            value-key="value"
            label-key="label"
          />
        </UFormField>

        <UFormField
          :label="t('table.seat')"
          name="seat"
        >
          <USelectMenu
            v-model="state.seat"
            class="w-full"
            :items="optionsSeat"
            value-key="value"
            label-key="label"
          />
        </UFormField>
        <UFormField
          :label="t('table.year')"
          name="year"
        >
          <USelectMenu
            v-model="state.year"
            class="w-full"
            :items="optionsModelYear"
            value-key="value"
            label-key="label"
          />
        </UFormField>

        <div class="flex justify-end gap-2">
          <UButton
            :label="t('cancel')"
            color="neutral"
            variant="subtle"
            @click="resetForm"
          />
          <UButton
            :label="t('save')"
            color="primary"
            variant="solid"
            type="submit"
            loading-auto
          />
        </div>
      </UForm>
    </template>
  </UModal>
</template>
