import * as z from "zod";

export const userSchema = z.object({
  id: z.string().optional(),
  email: z.string(),
  fullname: z.string(),
  jobTitle: z.string(),
  notificationCount: z.number().min(0).optional(),
  organizationId: z.string().optional(),
  organzationName: z.string().optional(),
  permissionId: z.string().optional(),
  permissionName: z.string().optional(),
  phone: z.string().optional(),
  signatureLevel: z.number().min(0).optional(),
  username: z.string().min(2).max(100).optional(),
});
