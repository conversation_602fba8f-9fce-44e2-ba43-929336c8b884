<script setup lang="ts">
import { CalendarDate, DateFormatter, getLocalTimeZone } from "@internationalized/date";
import type { Range } from "~/types";

const { locale } = useI18n();

const formatCode = computed(() => {
  return locale.value === "th" ? "th-TH" : "en-US";
});

const df = new DateFormatter(formatCode.value, {
  dateStyle: "medium",
});

const selected = defineModel<Range>({ required: true });

const toCalendarDate = (date: Date) => {
  return new CalendarDate(
    date.getFullYear(),
    date.getMonth() + 1,
    date.getDate(),
  );
};

const calendarRange = computed({
  get: () => ({
    start: selected.value.start ? toCalendarDate(selected.value.start) : undefined,
    end: selected.value.end ? toCalendarDate(selected.value.end) : undefined,
  }),
  set: (newValue: { start: CalendarDate | null; end: CalendarDate | null }) => {
    selected.value = {
      start: newValue.start ? newValue.start.toDate(getLocalTimeZone()) : new Date(),
      end: newValue.end ? newValue.end.toDate(getLocalTimeZone()) : new Date(),
    };
  },
});
</script>

<template>
  <UPopover>
    <UButton
      class="bg-white"
      color="neutral"
      variant="subtle"
      icon="i-lucide-calendar"
    >
      <template v-if="calendarRange.start">
        <template v-if="calendarRange.end">
          {{ df.format(calendarRange.start.toDate(getLocalTimeZone())) }} - {{ df.format(calendarRange.end.toDate(getLocalTimeZone())) }}
        </template>

        <template v-else>
          {{ df.format(calendarRange.start.toDate(getLocalTimeZone())) }}
        </template>
      </template>
      <template v-else>
        Pick a date
      </template>
    </UButton>

    <template #content>
      <UCalendar
        v-model="calendarRange"
        class="p-2"
        :number-of-months="2"
        range
      />
    </template>
  </UPopover>
</template>
