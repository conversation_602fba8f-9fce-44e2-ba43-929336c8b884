<script lang="ts" setup>
const { t } = useI18n();

interface Props {
  page?: number;
  totalPages?: number;
  totalRecords?: number;
  perPages?: number;
}
interface Emit {
  (e: "update:modelValue", value: number): void;
}

const emit = defineEmits<Emit>();
withDefaults(defineProps<Props>(), {
  page: 1,
  totalPages: 10,
  totalRecords: 0,
  perPages: 10,
});
</script>

<template>
  <div
    class="flex items-center justify-between gap-3 border-t border-default pt-4 mt-auto"
  >
    <div class="text-sm text-muted">
      {{
        t("table.footer", {
          page: page,
          total: totalPages,
          totalItems: totalRecords || 0,
        })
      }}
    </div>

    <div class="flex items-center gap-1.5">
      <UPagination
        :default-page="
          page
        "
        :items-per-page="perPages"
        :total="totalRecords"
        @update:page="(p: number) => emit('update:modelValue', p - 1)"
      />
    </div>
  </div>
</template>
