<script lang="ts" setup>
import { storeToRefs } from "pinia";
import { useOptionsStore } from "~/stores/options";

definePageMeta({
  middleware: ["authenticated"],
});

const { t } = useI18n();
const optionsStore = useOptionsStore();
const { optionsLoginBy } = storeToRefs(optionsStore);

const form = reactive({
  siteName: "",
  siteDescription: "",
  siteTimezone: "",
  loginBy: ["username"],
  siteColor: "#ffffff",
  siteColorHeader: "#ffffff",
  siteTextColor: "#000000",
  siteLogo: "",
});
</script>

<template>
  <div class="flex items-center gap-2 mb-4">
    <UIcon
      name="i-lucide-globe"
      class="size-5"
    />
    <h1 class="text-xl">
      {{ t("navSetting.site") }}
    </h1>

    <UButton
      form="settings"
      :label="t('save')"
      color="success"
      type="submit"
      class="w-fit ms-auto"
    />
  </div>

  <!--  general settings -->
  <UPageCard
    variant="subtle"
    :title="t('formSite.title')"
    class="mb-4"
  >
    <!-- Grid container -->
    <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
      <!-- Site name -->
      <UFormField
        :label="t('formSite.siteName')"
        class="w-full md:col-span-1"
      >
        <UInput
          v-model="form.siteName"
          class="w-full"
        />
      </UFormField>

      <!-- Timezone (pair it next to name on md+) -->
      <UFormField
        :label="t('formSite.siteTimezone')"
        class="w-full md:col-span-1"
      >
        <UInput
          v-model="form.siteTimezone"
          class="w-full"
        />
      </UFormField>

      <!-- Description spans full width -->
      <UFormField
        :label="t('formSite.siteDescription')"
        class="w-full md:col-span-2"
      >
        <UTextarea
          v-model="form.siteDescription"
          class="w-full"
        />
      </UFormField>

      <!-- LoginBy + SiteColor (side-by-side on md+) -->
      <UFormField
        :label="t('formSite.loginBy')"
        class="w-full md:col-span-1"
      >
        <UCheckboxGroup
          v-model="form.loginBy"
          variant="list"
          :default-value="['username']"
          item-value="code"
          :items="optionsLoginBy"
          class="w-full"
        />
      </UFormField>

      <UPopover>
        <UFormField
          :label="t('formSite.siteColor')"
          class="w-full md:col-span-1"
        >
          <UInput
            v-model="form.siteColor"
            class="w-full"
          >
            <template #trailing>
              <UBadge
                :style="form.siteColor ? `background-color: ${form.siteColor}` : ''"
                size="md"
                class="min-w-16 justify-center"
              >
                {{ form.siteColor || '-' }}
              </UBadge>
            </template>
          </UInput>
        </UFormField>
        <template #content>
          <UColorPicker
            v-model="form.siteColor"
            class="p-2"
          />
        </template>
      </UPopover>

      <!-- Header/Text colors (side-by-side on md+) -->
      <UPopover>
        <UFormField
          :label="t('formSite.siteColorHeader')"
          class="w-full md:col-span-1"
        >
          <UInput
            v-model="form.siteColorHeader"
            class="w-full"
          >
            <template #trailing>
              <UBadge
                :style="form.siteColorHeader ? `background-color: ${form.siteColorHeader}` : ''"
                size="md"
                class="min-w-16 justify-center"
              >
                {{ form.siteColorHeader || '-' }}
              </UBadge>
            </template>
          </UInput>
        </UFormField>
        <template #content>
          <UColorPicker
            v-model="form.siteColorHeader"
            class="p-2"
          />
        </template>
      </UPopover>

      <UPopover>
        <UFormField
          :label="t('formSite.siteTextColor')"
          class="w-full md:col-span-1"
        >
          <UInput
            v-model="form.siteTextColor"
            class="w-full"
          >
            <template #trailing>
              <UBadge
                :style="form.siteTextColor ? `background-color: ${form.siteTextColor}` : ''"
                size="md"
                class="min-w-16 justify-center"
              >
                {{ form.siteTextColor || '-' }}
              </UBadge>
            </template>
          </UInput>
        </UFormField>
        <template #content>
          <UColorPicker
            v-model="form.siteTextColor"
            class="p-2"
          />
        </template>
      </UPopover>

      <!-- Logo row: preview + file input -->
      <div class="flex items-center gap-4 md:col-span-1">
        <img
          src="https://github.com/benjamincanac.png"
          alt="Logo preview"
          class="h-32 w-32 rounded-lg object-cover"
        >
        <UButton color="error">
          {{ t('deleteImage') }}
        </UButton>
      </div>

      <UFormField
        :label="t('formSite.siteLogo')"
        class="w-full md:col-span-1"
      >
        <UInput
          v-model="form.siteLogo"
          type="file"
          class="w-full"
        />
      </UFormField>
    </div>
  </UPageCard>
</template>
