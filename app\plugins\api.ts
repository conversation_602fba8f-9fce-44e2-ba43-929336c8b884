import { ResponseApiError } from "~/error/api-error";

export default defineNuxtPlugin((nuxtApp) => {
  const store = useProfileStore();
  const { token } = storeToRefs(store);
  const toast = useToast();

  const api = $fetch.create({
    onRequest({ options }) {
      if (token.value) {
        // note that this relies on ofetch >= 1.4.0 - you may need to refresh your lockfile
        options.headers.set("Authorization", `Bearer ${token.value.access_token}`);
      }
    },
    async onResponseError({ response }) {
      if (response.status === 401) {
        await nuxtApp.runWithContext(() => navigateTo("/"));
      }
      if (response?.status === 401) {
        // useRouter().push("/");
      }
      else {
        // Handle other errors
        if (response?.status === 404) {
          throw new ResponseApiError("NOT_FOUND", "Resource not found", 404);
        }
        else {
          // Create a custom error with more details
          toast.add({
            title: response?.statusText || "API Error",
            description: response?._data?.message || "An unexpected error occurred.",
            icon: "i-lucide-x",
            progress: false,
            color: "error",
          });
        }
      }
    },
  });

  // Expose to useNuxtApp().$api
  return {
    provide: {
      api,
    },
  };
});
