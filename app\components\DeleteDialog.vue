<script setup lang="ts">
import type { ApiResponse } from "~/types";

const { t } = useI18n();
const { $api } = useNuxtApp();
const toast = useToast();

interface Emit {
  (e: "update:isDialogVisible", value: boolean): void;
  (e: "submit"): void;
}
interface Props {
  url?: string;
  isDialogVisible: boolean;
}
const props = defineProps<Props>();

const emit = defineEmits<Emit>();

const isDialogVisible = ref(props.isDialogVisible);
watch(props, () => {
  isDialogVisible.value = props.isDialogVisible;
});

async function onSubmit() {
  const response = await $api(props?.url ?? "", {
    method: "DELETE",
  });
  if (response) {
    const data = response as ApiResponse;
    toast.add({
      title: data.code,
      description: data.message,
      icon: data.status ? "i-lucide-check" : "i-lucide-x",
      color: data.status ? "success" : "error",
    });
    if (!data.status) {
      return;
    }
  }
  emit("update:isDialogVisible", false);
  emit("submit");
}
</script>

<template>
  <UModal
    v-model:open="isDialogVisible"
    :title="t('confirm')"

    @update:open="(value) => emit('update:isDialogVisible', value)"
  >
    <template #body>
      <div class="text-center">
        <UIcon
          name="i-lucide-alert-triangle"
          class="size-10 text-error"
        />
        <p class="mt-4">
          {{ t("confirmDelete") }}
        </p>
      </div>
      <div class="flex justify-end gap-2">
        <UButton
          :label="t('cancel')"
          color="neutral"
          variant="subtle"
          @click="emit('update:isDialogVisible', false)"
        />
        <UButton
          :label="$t('delete')"
          color="success"
          variant="solid"
          loading-auto
          @click="onSubmit"
        />
      </div>
    </template>
  </UModal>
</template>
