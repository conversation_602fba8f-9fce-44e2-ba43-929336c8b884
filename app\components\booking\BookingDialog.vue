<script setup lang="ts">
import type { Booking } from "~/types";

const { t } = useI18n();

interface Emit {
  (e: "update:isDialogVisible", value: boolean): void;
}
interface Props {
  booking?: Booking;
  isDialogVisible: boolean;
}

const emit = defineEmits<Emit>();
const props = withDefaults(defineProps<Props>(), {
  booking: () => ({
    id: "",
    organizationId: "",
    organizationName: "",
    projectName: "",
    vehicleId: "",
    vehicleModel: "",
    description: "",
    start: "",
    end: "",
    passengers: 0,
    driverName: "",
    remark: "",
    reservedBy: "",
    phone: "",
    rejectionReasons: "",
    status: "",
    reservedDate: "",
  }),
  isDialogVisible: false,
});

const isDialogVisible = ref(props.isDialogVisible);
const state = ref<Partial<Booking>>(structuredClone(toRaw(props.booking)));

watch(props, () => {
  state.value = structuredClone(toRaw(props.booking));
  isDialogVisible.value = props.isDialogVisible;
});
</script>

<template>
  <UModal
    v-model:open="isDialogVisible"
    :title="t('booking.title')"
    :width="600"
    :class="['max-w-2xl']"
    @update:open="(value) => emit('update:isDialogVisible', value)"
  >
    <template #body>
      <div class="space-y-2 items-center">
        <div class="flex flex-row gap-4 mb-2">
          <div class="font-bold basis-1/3">
            {{ t("booking.usageDetails") }}
          </div>
          <div class="text-muted basis-2/3">
            {{ state.projectName }}
            <br>
            {{ state.description }}
          </div>
        </div>
        <USeparator />
        <div class="flex flex-row gap-4 mb-2">
          <div class="font-bold basis-1/3">
            {{ t("booking.numberOfTravelers") }}
          </div>
          <div class="text-muted basis-2/3">
            {{ state.passengers }}
          </div>
        </div>
        <USeparator />
        <div class="flex flex-row gap-4 mb-2">
          <div class="font-bold basis-1/3">
            {{ t("booking.contactName") }}
          </div>
          <div class="text-muted basis-2/3">
            {{ state.reservedBy }}
          </div>
        </div>
        <USeparator />
        <div class="flex flex-row gap-4 mb-2">
          <div class="font-bold basis-1/3">
            {{ t("booking.vehicle") }}
          </div>
          <div class="text-muted basis-2/3">
            <UBadge
              size="xl"
              color="info"
            >
              {{ state.vehicleModel }}
            </UBadge>
          </div>
        </div>
        <!-- <USeparator />
        <div class="flex flex-row gap-4 mb-2">
          <div class="font-bold basis-1/3">
            {{ t("booking.type") }}
          </div>
          <div class="text-muted basis-2/3">
            {{ state.type }}
          </div>
        </div>
        <USeparator />
        <div class="flex flex-row gap-4 mb-2">
          <div class="font-bold basis-1/3">
            {{ t("booking.brand") }}
          </div>
          <div class="text-muted basis-2/3">
            {{ state.brand }}
          </div>
        </div> -->
        <USeparator />
        <div class="flex flex-row gap-4 mb-2">
          <div class="font-bold basis-1/3">
            {{ t("booking.chauffeur") }}
          </div>
          <div class="text-muted basis-2/3">
            {{ state.driverName }}
          </div>
        </div>
        <USeparator />
        <div class="flex flex-row gap-4 mb-2">
          <div class="font-bold basis-1/3">
            {{ t("booking.date") }}
          </div>
          <div class="text-muted basis-2/3">
            {{ state.start }} - {{ state.end }}
          </div>
        </div>
        <USeparator />
        <div class="flex flex-row gap-4 mb-2">
          <div class="font-bold basis-1/3">
            {{ t("booking.status") }}
          </div>
          <div class="text-muted basis-2/3">
            <UBadge
              size="xl"
              color="success"
            >
              {{ state.status }}
            </UBadge>
          </div>
        </div>
        <USeparator />
        <div class="flex flex-row gap-4 mb-2">
          <div class="font-bold basis-1/3">
            {{ t("booking.reason") }}
          </div>
          <div class="text-muted basis-2/3">
            {{ state.rejectionReasons }}
          </div>
        </div>
        <USeparator />
        <div class="flex flex-row gap-4 mb-2">
          <div class="font-bold basis-1/3">
            {{ t("booking.remark") }}
          </div>
          <div class="text-muted basis-2/3">
            {{ state.remark }}
          </div>
        </div>
      </div>
    </template>
  </UModal>
</template>
